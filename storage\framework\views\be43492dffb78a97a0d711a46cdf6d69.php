
<style>
.service-availibility-calendar .modal-dialog {
    max-width: 500px;
}
.service-availibility-calendar .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
}
.service-availibility-calendar .modal-header {
    border-bottom: none;
    padding: 24px 24px 0;
}
.service-availibility-calendar .modal-body {
    padding: 20px 24px;
}
.service-availibility-calendar .modal-footer {
    border-top: none;
    padding: 0 24px 24px;
    justify-content: space-between;
}
.service-availibility-calendar .day-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}
.service-availibility-calendar .day-row .day-label {
    font-weight: 500;
    color: #333;
    margin: 0;
    flex: 1;
}
.service-availibility-calendar .day-row .day-status {
    color: #6c757d;
    font-size: 14px;
}
.service-availibility-calendar .form-check-input {
    margin-right: 12px;
    margin-top: 0;
}
.service-availibility-calendar .btn-link {
    color: #6c757d;
    text-decoration: none;
}
.service-availibility-calendar .btn-link:hover {
    color: #495057;
}
.service-availibility-calendar .time-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
}
.service-availibility-calendar .time-inputs input {
    width: 80px;
    font-size: 12px;
    padding: 4px 8px;
}
</style>

<div class="modal fade service-availibility-calendar" id="availabilityModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content p-3">
            <div class="modal-header">
                <h5 class="modal-title">Availability</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="availability-calendar">
                    <!-- Week Navigation -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <button type="button" class="btn btn-link p-0" id="prevWeek">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <h6 class="mb-0 fw-bold" id="weekRange">11 Aug 2025 - 17 Aug 2025</h6>
                        <button type="button" class="btn btn-link p-0" id="nextWeek">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>

                    <!-- Days Container -->
                    <div id="weekDaysContainer" class="mb-4">
                        <!-- Default days structure -->
                        <div class="day-row" data-day="Monday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Monday">
                                <label class="day-label">Monday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Tuesday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Tuesday">
                                <label class="day-label">Tuesday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Wednesday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Wednesday">
                                <label class="day-label">Wednesday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Thursday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Thursday">
                                <label class="day-label">Thursday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Friday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Friday">
                                <label class="day-label">Friday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Saturday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Saturday">
                                <label class="day-label">Saturday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Sunday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Sunday">
                                <label class="day-label">Sunday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                    </div>

                    <!-- Recurring Checkbox -->
                    <div class="mb-4">
                        <label class="form-check">
                            <input class="form-check-input" type="checkbox" id="recurringCheckbox">
                            <span class="form-check-label">Recurring</span>
                        </label>
                    </div>

                    <!-- JSON Output (Hidden) -->
                    <textarea id="jsonOutput" style="display: none;"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveAvailability" data-bs-dismiss="modal">Done</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script>
// Wrap everything in IIFE to prevent variable conflicts
(function() {
    const weekData = {};
    let currentWeekIndex = 0;
    const baseStartDate = moment().startOf('isoWeek'); // This will set the start date to the current week's Monday

    // 🎯 JSON DATA INITIALIZATION - Pass your array JSON data here
    const initializeDataFromJSON = () => {
        // 📋 YOUR JSON DATA - Replace this array with your actual API response
        const availabilityArray = <?php echo json_encode(isset($service) ? $service->availabilities : [], 15, 512) ?>;

        // Convert array format to week-based format for the calendar
        availabilityArray.forEach(item => {
            const date = moment(item.date);
            const today = moment().startOf('day');

            // 🚫 SKIP PAST DATES - Don't process dates that are in the past
            if (date.isBefore(today, 'day')) {
                return; // Skip this iteration for past dates
            }

            const weekStart = date.clone().startOf('isoWeek'); // Get Monday of that week
            const weekKey = weekStart.format("YYYY-MM-DD");
            const dayName = item.day;

            // Convert time format from "HH:MM:SS" to "HH:MM"
            const startTime = item.start_time.substring(0, 5); // Remove seconds
            const endTime = item.end_time.substring(0, 5); // Remove seconds

            // Initialize week if it doesn't exist
            if (!weekData[weekKey]) {
                weekData[weekKey] = {
                    Monday: { enabled: false, start: "10:00", end: "19:00" },
                    Tuesday: { enabled: false, start: "10:00", end: "19:00" },
                    Wednesday: { enabled: false, start: "10:00", end: "19:00" },
                    Thursday: { enabled: false, start: "10:00", end: "19:00" },
                    Friday: { enabled: false, start: "10:00", end: "19:00" },
                    Saturday: { enabled: false, start: "10:00", end: "19:00" },
                    Sunday: { enabled: false, start: "10:00", end: "19:00" }
                };
            }

            // Set the specific day data (only for current and future dates)
            weekData[weekKey][dayName] = {
                enabled: true,
                start: startTime,
                end: endTime,
                id: item.id, // Store original ID for reference
                service_id: item.service_id // Store service_id for reference
            };
        });
    };

    // 🎯 EXTRACT SELECTED AVAILABILITY IN YOUR DESIRED FORMAT
    const getSelectedAvailability = () => {
        const selectedAvailability = [];

        // Loop through all weeks in weekData
        Object.keys(weekData).forEach(weekKey => {
            const weekStart = moment(weekKey); // Monday of the week
            const weekDays = weekData[weekKey];

            // Check each day of the week
            Object.keys(weekDays).forEach(dayName => {
                const dayData = weekDays[dayName];

                // Only include enabled days
                if (dayData.enabled) {
                    // Calculate the actual date for this day
                    const dayIndex = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"].indexOf(dayName);
                    const actualDate = weekStart.clone().add(dayIndex, 'days');

                    // Add to result array in your desired format
                    selectedAvailability.push({
                        "date": actualDate.format("YYYY-MM-DD"),
                        "day": dayName,
                        "start": dayData.start,
                        "end": dayData.end
                    });
                }
            });
        });

        // Sort by date for better organization
        selectedAvailability.sort((a, b) => moment(a.date).diff(moment(b.date)));
        return selectedAvailability;
    };

    // 📝 UPDATE TEXTAREA WITH JSON OUTPUT
    const updateJsonOutput = () => {
        const selectedData = getSelectedAvailability();
        const jsonString = JSON.stringify(selectedData, null, 2);
        $("#jsonOutput").val(jsonString);
    };

    const updateWeekUI = () => {
        const startOfWeek = baseStartDate.clone().add(currentWeekIndex * 7, "days");
        const weekDays = Array.from({ length: 7 }, (_, i) => startOfWeek.clone().add(i, "days"));
        const weekRange = `${weekDays[0].format("DD MMM YYYY")} - ${weekDays[6].format("DD MMM YYYY")}`;
        const weekKey = startOfWeek.format("YYYY-MM-DD");
        const week = weekData[weekKey] || {};

        $("#weekRange").text(weekRange);

        // Update navigation buttons
        $("#prevWeek").prop('disabled', currentWeekIndex <= 0);

        const dayNames = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
        const today = moment().startOf('day');

        // Update existing day rows with saved data
        dayNames.forEach((day, index) => {
            const date = weekDays[index];
            const val = week[day] || { start: "10:00", end: "19:00", enabled: false };
            const isPastDate = date.isBefore(today, 'day');

            const $dayRow = $(`.day-row[data-day="${day}"]`);
            const $checkbox = $dayRow.find('.day-checkbox');

            // Set data-date attribute
            $dayRow.attr('data-date', date.format("YYYY-MM-DD"));

            // Update checkbox state without triggering change event
            $checkbox.off('change');
            $checkbox.prop('checked', val.enabled && !isPastDate);
            $checkbox.prop('disabled', isPastDate);

            // Update content based on state
            if (val.enabled && !isPastDate) {
                // Show time inputs
                const $status = $dayRow.find('.day-status');
                if ($status.length > 0) {
                    $status.replaceWith(`
                        <div class="time-inputs">
                            <input type="time" class="form-control form-control-sm start-time" value="${val.start}" data-day="${day}">
                            <span>to</span>
                            <input type="time" class="form-control form-control-sm end-time" value="${val.end}" data-day="${day}">
                        </div>
                    `);
                } else {
                    $dayRow.find('.start-time').val(val.start);
                    $dayRow.find('.end-time').val(val.end);
                }
            } else {
                // Show status text
                const statusText = isPastDate ? "Past Date" : "Closed";
                const $timeInputs = $dayRow.find('.time-inputs');
                if ($timeInputs.length > 0) {
                    $timeInputs.replaceWith(`<span class="day-status">${statusText}</span>`);
                } else {
                    $dayRow.find('.day-status').text(statusText);
                }
            }

            // Re-attach change event
            $checkbox.on('change', function() {
                const $dayRow = $(this).closest('.day-row');
                const day = $(this).data('day');
                const isChecked = $(this).is(':checked');

                if (isChecked) {
                    const $status = $dayRow.find('.day-status');
                    if ($status.length > 0) {
                        $status.replaceWith(`
                            <div class="time-inputs">
                                <input type="time" class="form-control form-control-sm start-time" value="10:00" data-day="${day}">
                                <span>to</span>
                                <input type="time" class="form-control form-control-sm end-time" value="19:00" data-day="${day}">
                            </div>
                        `);
                    }
                } else {
                    const $timeInputs = $dayRow.find('.time-inputs');
                    if ($timeInputs.length > 0) {
                        $timeInputs.replaceWith('<span class="day-status">Closed</span>');
                    }
                }

                saveCurrentWeekData();
                updateJsonOutput();
            });
        });
    };

    const saveCurrentWeekData = () => {
        const base = baseStartDate.clone().add(currentWeekIndex * 7, "days");
        const key = base.format("YYYY-MM-DD");
        weekData[key] = {};

        $(".day-row").each(function() {
            const day = $(this).data("day");
            const enabled = $(this).find(".day-checkbox").is(":checked");
            const $startTime = $(this).find(".start-time");
            const $endTime = $(this).find(".end-time");
            const start = $startTime.length > 0 ? $startTime.val() : "10:00";
            const end = $endTime.length > 0 ? $endTime.val() : "19:00";
            weekData[key][day] = { enabled, start, end };
        });

        console.log('Saved week data:', weekData[key]);
    };

    // Function to duplicate the weeks (with reset functionality)
    const duplicateWeeks = (weeks) => {
        const current = baseStartDate.clone().add(currentWeekIndex * 7, "days");
        const srcKey = current.format("YYYY-MM-DD");

        // 🔄 RESET: Clear all future week duplications first
        Object.keys(weekData).forEach(weekKey => {
            const weekDate = moment(weekKey);
            const currentWeekDate = moment(srcKey);

            // Remove any week that's after the current week and was previously duplicated
            if (weekDate.isAfter(currentWeekDate, 'week')) {
                // Check if this week has the same pattern as current week (indicating it was duplicated)
                const currentWeekData = weekData[srcKey];
                const weekToCheck = weekData[weekKey];

                // Only remove if it looks like a duplication (same enabled pattern)
                if (currentWeekData && weekToCheck) {
                    const currentEnabledDays = Object.keys(currentWeekData).filter(day => currentWeekData[day].enabled);
                    const checkEnabledDays = Object.keys(weekToCheck).filter(day => weekToCheck[day].enabled);

                    // If same number of enabled days, likely a duplication - remove it
                    if (currentEnabledDays.length === checkEnabledDays.length && currentEnabledDays.length > 0) {
                        delete weekData[weekKey];
                    }
                }
            }
        });

        // 📅 CREATE: Now create fresh duplications for the selected weeks
        for (let i = 1; i < weeks; i++) {
            const next = current.clone().add(i * 7, "days");
            const newKey = next.format("YYYY-MM-DD");
            // Create deep copy of current week's data
            weekData[newKey] = JSON.parse(JSON.stringify(weekData[srcKey]));
        }
    };

    // Validate time inputs
    const validateTimeInput = (input) => {
        const $input = $(input);
        const startTime = $input.val();
        const endTimeInput = $input.hasClass('start-time') ? 
            $input.closest('.day-row').find('.end-time') : 
            $input.closest('.day-row').find('.start-time');
        const endTime = endTimeInput.val();

        // Basic validation to ensure start time is before end time
        if (startTime && endTime && startTime >= endTime) {
            alert('Start time must be before end time');
            $input.focus();
        }
    };

    // Make functions globally available for form submission
    window.getSelectedAvailability = getSelectedAvailability;
    window.saveCurrentWeekData = saveCurrentWeekData;

    $(document).ready(function() {
        // Initialize data from JSON
        initializeDataFromJSON();
        updateWeekUI();
        updateJsonOutput();

        // Initialize modal when it's shown
        $('#availabilityModal').on('shown.bs.modal', function() {
            updateWeekUI();
            updateJsonOutput();
        });



        // Validate time inputs when they change
        $(document).on("change", ".start-time, .end-time", function() {
            validateTimeInput(this);
            saveCurrentWeekData();
            updateJsonOutput(); // Update JSON when time changes
        });

        $("#prevWeek").click(function() {
            // Prevent going to past weeks
            if (currentWeekIndex > 0) {
                saveCurrentWeekData();
                currentWeekIndex--;
                updateWeekUI();
                updateJsonOutput(); // Update JSON when week changes
            }
        });

        $("#nextWeek").click(function() {
            saveCurrentWeekData();
            currentWeekIndex++;
            updateWeekUI();
            updateJsonOutput(); // Update JSON when week changes
        });

        $("#saveAvailability").click(function() {
            saveCurrentWeekData();
            updateJsonOutput(); // Final update of JSON
            const selectedData = getSelectedAvailability();
            console.log("Availability saved:", selectedData);
        });

        // Recurring Checkbox Change
        $("#recurringCheckbox").change(function() {
            const isChecked = $(this).is(':checked');
            if (isChecked) {
                saveCurrentWeekData();
                // Duplicate for 4 weeks by default when recurring is enabled
                duplicateWeeks(4);
                updateJsonOutput();
                console.log('Recurring enabled - duplicated for 4 weeks');
            } else {
                // Clear future weeks when recurring is disabled
                const current = baseStartDate.clone().add(currentWeekIndex * 7, "days");
                const srcKey = current.format("YYYY-MM-DD");

                Object.keys(weekData).forEach(weekKey => {
                    const weekDate = moment(weekKey);
                    const currentWeekDate = moment(srcKey);

                    if (weekDate.isAfter(currentWeekDate, 'week')) {
                        delete weekData[weekKey];
                    }
                });
                updateJsonOutput();
                console.log('Recurring disabled - cleared future weeks');
            }
        });
    });
})();
</script>
<?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/service/include/availability-modal.blade.php ENDPATH**/ ?>
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Events\MessageSent;
use App\Events\MessageDelivered;
use App\Events\MessageRead;
use App\Events\UserTyping;
use App\Events\UserMessageReceived;

class ChatController extends Controller
{
    /**
     * Display the chat interface
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $conversationIdParam = $request->get('conversation_id');
        $professionalId = $request->get('professional_id');

        // Convert conversation_id from IDS to database ID if needed
        $conversationId = null;
        if ($conversationIdParam) {
            // Try to find by IDS first, then by database ID
            $conversation = Conversation::where('ids', $conversationIdParam)->first();
            if (!$conversation) {
                $conversation = Conversation::find($conversationIdParam);
            }
            $conversationId = $conversation ? $conversation->id : null;
        }

        // If professional_id is provided, create or get conversation and redirect
        if ($professionalId) {
            $professional = User::where('ids', $professionalId)->first();
            if ($professional && $user->hasRole('customer')) {
                $conversation = Conversation::createOrGet($user->id, $professional->id);
                // Redirect to clean URL with conversation_id (using IDS)
                return redirect()->route('chats.index', ['conversation_id' => $conversation->ids]);
            }
        }

        return view('dashboard.chats.index', compact('conversationId'));
    }

    /**
     * Get conversations for sidebar
     */
    public function getConversations()
    {
        try {
            $user = Auth::user();

            // Get conversations directly using Conversation model
            $conversations = Conversation::where(function($query) use ($user) {
                    $query->where('sender_id', $user->id)
                          ->orWhere('receiver_id', $user->id);
                })
                ->where('is_active', true)
                ->with(['sender.profile', 'receiver.profile', 'latestMessage.sender'])
                ->orderBy('last_message_at', 'desc')
                ->get()
                ->map(function ($conversation) use ($user) {
                    $otherUser = $conversation->getOtherParticipant($user->id);
                    return [
                        'id' => $conversation->id,
                        'ids' => $conversation->ids,
                        'other_user' => [
                            'id' => $otherUser->id,
                            'name' => $otherUser->name,
                            'profile_pic' => $otherUser->profile->pic ?? null,
                            'role' => $otherUser->getRoleNames()->first(),
                            'is_online' => $otherUser->is_online ?? false,
                            'online_at' => $otherUser->online_at
                        ],
                        'last_message' => $conversation->latestMessage ? [
                            'content' => $this->getLastMessageContent($conversation->latestMessage),
                            'created_at' => $conversation->latestMessage->created_at->diffForHumans(),
                            'is_sender' => $conversation->latestMessage->sender_id == $user->id,
                            'sender_name' => $conversation->latestMessage->sender->name
                        ] : null,
                        'unread_count' => $conversation->getUnreadCount($user->id),
                        'updated_at' => $conversation->last_message_at
                    ];
                });

            return response()->json([
                'success' => true,
                'conversations' => $conversations
            ]);
        } catch (\Exception $e) {
            Log::error('Error loading conversations: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error loading conversations: ' . $e->getMessage(),
                'conversations' => []
            ], 500);
        }
    }

    /**
     * Get messages for a conversation
     */
    public function getMessages(Request $request, $conversationId)
    {
        $user = Auth::user();
        $page = $request->get('page', 1);
        $perPage = 20;

        $conversation = Conversation::find($conversationId);

        if (!$conversation || !$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // Mark messages as read
        $conversation->markAsRead($user->id);

        $messages = $conversation->messages()
            ->with('sender.profile')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        $formattedMessages = $messages->items();
        $formattedMessages = array_reverse($formattedMessages);

        $messagesData = collect($formattedMessages)->map(function ($message) use ($user) {
            return [
                'id' => $message->id,
                'content' => $message->content,
                'message_type' => $message->message_type,
                'attachments' => $message->getFormattedAttachments(),
                'sender' => [
                    'id' => $message->sender->id,
                    'name' => $message->sender->name,
                    'profile_pic' => $message->sender->profile->pic ?? null
                ],
                'is_sender' => $message->sender_id == $user->id,
                'sending_status' => $message->sending_status,
                'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                'formatted_time' => $message->created_at->format('H:i'),
                'is_edited' => $message->is_edited
            ];
        });

        return response()->json([
            'success' => true,
            'messages' => $messagesData,
            'has_more' => $messages->hasMorePages(),
            'current_page' => $messages->currentPage(),
            'conversation' => [
                'id' => $conversation->id,
                'other_user' => [
                    'id' => $conversation->getOtherParticipant($user->id)->id,
                    'name' => $conversation->getOtherParticipant($user->id)->name,
                    'profile_pic' => $conversation->getOtherParticipant($user->id)->profile->pic ?? null,
                    'is_online' => $conversation->getOtherParticipant($user->id)->is_online ?? false,
                    'online_at' => $conversation->getOtherParticipant($user->id)->online_at
                ]
            ]
        ]);
    }

    /**
     * Send a new message
     */
    public function sendMessage(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|exists:conversations,id',
            'content' => 'required_without:attachments|string|max:5000',
            'message_type' => 'in:text,image,video,document',
            'attachments.*' => 'file|max:50000', // 50MB max per file
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $conversation = Conversation::find($request->conversation_id);

        if (!$conversation || !$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // Handle file attachments
        $attachments = [];
        if ($request->hasFile('attachments')) {
            $attachments = $this->handleFileUploads($request->file('attachments'));
        }

        // Create message
        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $user->id,
            'content' => $request->content,
            'message_type' => $request->message_type ?? 'text',
            'attachments' => $attachments,
            'sending_status' => 'sent',
            'sent_at' => now()
        ]);

        // Update conversation last message
        $conversation->updateLastMessage($message->id);

        // Load relationships for response
        $message->load('sender.profile');

        $messageData = [
            'id' => $message->id,
            'content' => $message->content,
            'message_type' => $message->message_type,
            'attachments' => $message->getFormattedAttachments(),
            'sender' => [
                'id' => $message->sender->id,
                'name' => $message->sender->name,
                'profile_pic' => $message->sender->profile->pic ?? null
            ],
            'is_sender' => true,
            'sending_status' => $message->sending_status,
            'created_at' => $message->created_at->format('Y-m-d H:i:s'),
            'formatted_time' => $message->created_at->format('H:i'),
            'conversation_id' => $conversation->id
        ];

        // Broadcast message via Pusher to conversation channel
        \Log::info('Broadcasting message to conversation: ' . $conversation->id, $messageData);
        broadcast(new MessageSent($messageData, $conversation->id, $conversation->ids))->toOthers();

        // Also broadcast to the receiver's user-specific channel for delivery notifications
        $receiverId = $conversation->sender_id == $user->id ? $conversation->receiver_id : $conversation->sender_id;
        \Log::info('🔔 BACKEND: Broadcasting message to user channel: user.' . $receiverId);
        \Log::info('🔔 BACKEND: Sender ID: ' . $user->id . ', Receiver ID: ' . $receiverId);
        \Log::info('🔔 BACKEND: Message data: ', $messageData);

        try {
            broadcast(new UserMessageReceived($messageData, $conversation->id, $conversation->ids, $receiverId))->toOthers();
            \Log::info('🔔 BACKEND: UserMessageReceived event broadcast successfully');
        } catch (\Exception $e) {
            \Log::error('🔔 BACKEND: Error broadcasting UserMessageReceived: ' . $e->getMessage());
        }

        // Don't auto-mark as delivered here - let the frontend handle it based on user online status
        // The message will be marked as delivered when the receiver comes online and receives the Pusher event

        return response()->json([
            'success' => true,
            'message' => $messageData
        ]);
    }

    /**
     * Handle file uploads without compression
     */
    private function handleFileUploads($files)
    {
        $attachments = [];
        $imageCount = 0;
        $videoCount = 0;
        $docCount = 0;

        foreach ($files as $file) {
            $mimeType = $file->getMimeType();
            $fileType = $this->getFileType($mimeType);

            // Check limits
            if ($fileType === 'image' && $imageCount >= 10) continue;
            if ($fileType === 'video' && $videoCount >= 3) continue;
            if ($fileType === 'document' && $docCount >= 5) continue;

            // Use storeImage method for all file types
            $path = $this->storeImage("chats/{$fileType}s", $file);

            if ($fileType === 'image') {
                $imageCount++;
            } elseif ($fileType === 'video') {
                $videoCount++;
            } else {
                $docCount++;
            }

            $attachments[] = [
                'name' => $file->getClientOriginalName(),
                'path' => $path,
                'type' => $fileType,
                'size' => $file->getSize(),
                'mime_type' => $mimeType
            ];
        }

        return $attachments;
    }

    /**
     * Handle file uploads via AJAX
     */
    public function uploadFiles(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|exists:conversations,id',
            'files.*' => 'required|file|max:51200', // 50MB max per file
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $conversation = Conversation::findOrFail($request->conversation_id);
            $user = Auth::user();

            // Check if user is part of the conversation
            if (!$conversation->isParticipant($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to conversation'
                ], 403);
            }

            // Handle file attachments
            $attachments = [];
            if ($request->hasFile('files')) {
                $attachments = $this->handleFileUploads($request->file('files'));
            }

            // Create message with attachments
            $message = Message::create([
                'conversation_id' => $conversation->id,
                'sender_id' => $user->id,
                'content' => '', // Empty content for file-only messages
                'attachments' => $attachments,
                'message_type' => 'file',
                'sending_status' => 'sent',
                'sent_at' => now()
            ]);

            // Update conversation's last message
            $conversation->update([
                'last_message_id' => $message->id,
                'last_message_at' => now()
            ]);

            // Broadcast the message
            $messageData = [
                'id' => $message->id,
                'conversation_id' => $conversation->id,
                'content' => $message->content,
                'message_type' => $message->message_type,
                'attachments' => $message->getFormattedAttachments(),
                'sender' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'profile_pic' => $user->profile->pic ?? null
                ],
                'is_sender' => true,
                'sending_status' => $message->sending_status,
                'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                'formatted_time' => $message->created_at->format('H:i'),
                'is_edited' => $message->is_edited
            ];

            broadcast(new MessageSent($messageData, $conversation->id, $conversation->ids));

            return response()->json([
                'success' => true,
                'message' => 'Files uploaded successfully',
                'data' => [
                    'message_id' => $message->id,
                    'attachments' => $attachments,
                    'message_data' => $messageData
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get appropriate content for last message display
     */
    private function getLastMessageContent($message)
    {
        // If it's a text message, return the content
        if ($message->message_type === 'text' && !empty($message->content)) {
            return $message->content;
        }

        // If it's a file message, return appropriate text based on attachments
        if ($message->message_type === 'file' && $message->hasAttachments()) {
            $attachments = $message->attachments;

            // Handle both array and string cases for backward compatibility
            if (is_string($attachments)) {
                $attachments = json_decode($attachments, true) ?? [];
            }

            if (!empty($attachments)) {
                $firstAttachment = $attachments[0];
                $type = $firstAttachment['type'] ?? 'file';
                $count = count($attachments);

                switch ($type) {
                    case 'image':
                        return $count > 1 ? "📷 {$count} photos" : "📷 Photo";
                    case 'video':
                        return $count > 1 ? "🎥 {$count} videos" : "🎥 Video";
                    case 'document':
                        return $count > 1 ? "📄 {$count} documents" : "📄 Document";
                    default:
                        return $count > 1 ? "📎 {$count} files" : "📎 File";
                }
            }
        }

        // Fallback for other message types or empty content
        return $message->content ?: 'Message';
    }

    /**
     * Determine file type from mime type
     */
    private function getFileType($mimeType)
    {
        if (strpos($mimeType, 'image/') === 0) {
            return 'image';
        } elseif (strpos($mimeType, 'video/') === 0) {
            return 'video';
        } else {
            return 'document';
        }
    }

    /**
     * Mark message as delivered
     */
    public function markAsDelivered(Request $request, $messageId)
    {
        $user = Auth::user();
        $message = Message::find($messageId);

        if (!$message) {
            return response()->json(['error' => 'Message not found'], 404);
        }

        // Check if user is participant in this conversation
        $conversation = $message->conversation;
        if (!$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Only update if message is not from current user and status is 'sent'
        if ($message->sender_id != $user->id && $message->sending_status == 'sent') {
            \Log::info("Marking message {$message->id} as delivered");
            $message->update([
                'sending_status' => 'delivered',
                'delivered_at' => now()
            ]);

            // Broadcast delivered status to sender
            broadcast(new \App\Events\MessageDelivered($message->id, $conversation->id))->toOthers();
            \Log::info("Broadcasted delivered status for message {$message->id}");
        } else {
            \Log::info("Not marking message {$message->id} as delivered. Sender: {$message->sender_id}, Current user: {$user->id}, Status: {$message->sending_status}");
        }

        return response()->json(['success' => true]);
    }

    /**
     * Mark messages as read
     */
    public function markAsRead(Request $request, $conversationId)
    {
        $user = Auth::user();
        $conversation = Conversation::find($conversationId);

        if (!$conversation || !$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // Get unread message IDs before marking as read
        $unreadMessageIds = $conversation->messages()
            ->where('sender_id', '!=', $user->id)
            ->whereNull('read_at')
            ->pluck('id')
            ->toArray();
        $conversation->markAsRead($user->id);

        // Broadcast read event if there were unread messages
        if (!empty($unreadMessageIds)) {
            broadcast(new MessageRead($unreadMessageIds, $conversation->id))->toOthers();
        }

        return response()->json(['success' => true]);
    }

    /**
     * Handle typing indicator
     */
    public function typing(Request $request, $conversationId)
    {
        $user = Auth::user();
        $conversation = Conversation::find($conversationId);

        if (!$conversation || !$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        $isTyping = $request->get('is_typing', true);

        // Broadcast typing event
        broadcast(new UserTyping($user, $conversation->id, $isTyping))->toOthers();

        return response()->json(['success' => true]);
    }

    /**
     * Archive a conversation
     */
    public function archiveConversation(Request $request, $conversationId)
    {
        $user = Auth::user();
        $conversation = Conversation::find($conversationId);

        if (!$conversation || !$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // For now, we'll just mark it as inactive for the user
        // In a more complex system, you might have a separate archived_conversations table
        $conversation->update(['is_active' => false]);

        return response()->json([
            'success' => true,
            'message' => 'Conversation archived successfully'
        ]);
    }

    /**
     * Delete a conversation
     */
    public function deleteConversation(Request $request, $conversationId)
    {
        $user = Auth::user();
        $conversation = Conversation::find($conversationId);

        if (!$conversation || !$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // Delete all messages in the conversation
        $conversation->messages()->delete();

        // Delete the conversation
        $conversation->delete();

        return response()->json([
            'success' => true,
            'message' => 'Conversation deleted successfully'
        ]);
    }

    /**
     * Get unread conversations count for envelope counter
     */
    public function getUnreadCount()
    {
        $user = auth()->user();

        // Count conversations with unread messages
        $unreadCount = Conversation::where(function($query) use ($user) {
            $query->where('sender_id', $user->id)
                  ->orWhere('receiver_id', $user->id);
        })
        ->whereHas('messages', function($query) use ($user) {
            $query->where('sender_id', '!=', $user->id)
                  ->whereNull('read_at');
        })
        ->count();

        return response()->json([
            'success' => true,
            'count' => $unreadCount
        ]);
    }
}

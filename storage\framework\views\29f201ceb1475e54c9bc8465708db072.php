<?php $__env->startPush('css'); ?>
    <style>
        .cursor-pointer {
            cursor: pointer;
        }

        #map {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: box-shadow 0.3s ease;
        }

        #map:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .favorite-icon {
            cursor: pointer;
        }

        .favorite-icon i {
            font-size: 1.5rem;
        }

        .favorite-icon i.fas.text-danger {
            color: #e91e63 !important;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard bg-color addfamily padding-block">
        <div id="kt_app_content_container" class="container py-5 px-5">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex gap-8 align-items-center ms-0 px-0">
                    <div class="pro-stylist-logo">
                        <img src="<?php echo e(asset('website') . '/' . $user->profile->pic ?? ''); ?>">
                    </div>

                    <div class="w-100">
                        <h5><?php echo e($user->name ?? ''); ?> <span class="top-rated ms-5">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/top-rated-star.png" class="img-fluid"> TOP
                                RATED </span> </h5>

                        <div class="d-flex align-items-center">
                            <p class="fs-16 semi-bold dark-blue"><span><i class="fas fa-star pe-3"></i></span>5.0
                                <span>(546)</span>
                            </p>
                            <ul class="d-flex gap-12 sub-details">
                                
                                <?php if($user->profile->city && $user->profile->country): ?>
                                    <li class="fs-16"><?php echo e($user->profile->city ?? ''); ?>,<?php echo e($user->profile->country ?? ''); ?>

                                    </li>
                                <?php endif; ?>
                                <li class="fs-16"><a href="#directions"><span class="fs-14 ms-5 deep-blue"> <i
                                                class="fas fa-external-link-alt fa-4"> </i> Get Directions </span></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="d-flex flex-end w-50 align-items-center">
                        <button style="all: unset" id="generateShortUrlBtn" data-user-id="<?php echo e($user->ids); ?>">
                            <img src="<?php echo e(asset('website')); ?>/assets/images/upload.svg">
                        </button>
                        <?php if(auth()->check() && auth()->user()->hasRole('customer')): ?>
                            <button class="favorite-icon main-heading-icon" style="all: unset" data-user-id="<?php echo e($user->id); ?>">
                                <i class="fa-heart fa-5 <?php echo e($isFavorited ? 'fas text-danger' : 'far'); ?>" id="heart-icon"></i>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <?php if($user->galleries->count() > 0): ?>
            <div class="container-fluid professional-swiper banner-swiper">
                <div class="row">
                    <div class="col-md-12">
                        <div class="swiper mySwiper">
                            <div class="swiper-wrapper">
                                <?php $__currentLoopData = $user->galleries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gallery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="swiper-slide"><img src="<?php echo e(asset('website') . '/' . $gallery->image); ?>">
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <div class="swiper-pagination"></div>

                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-pagination"></div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="container">
            <div class="row">
                <?php if($user->product_cerficates->count() > 0): ?>
                    <div class="col-md-12 mb-10">
                        <div class="swiper mySwiper mySwiper2 certifications-logo pt-15">
                            <p class="fs-16 Sora bold">Product Certifications</p>
                            <div class="swiper-wrapper">
                                <?php $__currentLoopData = $user->product_cerficates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productCertification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="swiper-slide">
                                        <img src="<?php echo e(asset('website') . '/' . $productCertification->image); ?>">
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-pagination"> </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if($user->introCards->count() > 0): ?>
                    <div class="col-md-12 mb-10">
                        <div class="swiper mySwiper guarantee-section ">
                            <div class="swiper-wrapper">
                                <?php $__currentLoopData = $user->introCards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $card): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="swiper-slide">
                                        <div class="d-flex gap-5 align-items-center cards">
                                            <img src="<?php echo e(asset('website') . '/' . $card->image); ?>" width="50px"
                                                height="50px">

                                            <div>
                                                <p class="fs-16 bold mb-0"><?php echo e($card->heading ?? ''); ?> </p>
                                                <p class="fs-16 bold light-gray mb-0"><?php echo e($card->description ?? ''); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>

                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-pagination"> </div>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="col-md-8">
                    <div class="container mb-5">
                        <div class="row">
                            <div class="col-md-12">
                                <!-- Search and Title Section -->
                                <div class="d-flex justify-content-between mb-10">
                                    <h4 class="fs-24 sora black">Services</h4>
                                    <div class="search-bar d-flex align-items-center">
                                        <i class="fa-solid fa-magnifying-glass me-3"></i>
                                        <input type="text" placeholder="Search">
                                    </div>
                                </div>

                                <!-- Categories Section with Filter Button in Same Line -->
                                <div class="d-flex justify-content-between align-items-center pro-service-tabs">
                                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                                        <!-- "All" Tab (Always Active) -->
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="all-services-tab" data-bs-toggle="pill"
                                                data-bs-target="#all-services" type="button" role="tab"
                                                aria-controls="all-services" aria-selected="true">
                                                All
                                            </button>
                                        </li>

                                        <!-- Category Tabs -->
                                        <?php $__currentLoopData = $userCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link category-tab-btn"
                                                    id="category-<?php echo e($category->id); ?>-tab" data-bs-toggle="pill"
                                                    data-bs-target="#category-<?php echo e($category->id); ?>" type="button"
                                                    role="tab" data-category-id="<?php echo e($category->id); ?>"
                                                    aria-controls="category-<?php echo e($category->id); ?>" aria-selected="false">
                                                    <?php echo e($category->name); ?>

                                                </button>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>

                                    
                                </div>

                                <!-- Tab Content Section -->
                                <div class="tab-content" id="pills-tabContent">

                                    <!-- All Services Tab (Always Visible) -->
                                    <div class="tab-pane fade show active" id="all-services" role="tabpanel"
                                        aria-labelledby="all-services-tab">
                                        <div class="row row-gap-5" id="services-list">
                                            <table id="responsiveTable" class=" display wallet-history-table"
                                                style="width: 100%">
                                                <thead>
                                                    <tr>
                                                        <th></th>
                                                        <th>Duration</th>
                                                        <th>Price</th>
                                                        <th>Category</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php $__empty_1 = true; $__currentLoopData = $user->services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                        <tr class="service-item" data-price="<?php echo e($service->price); ?>"
                                                            data-duration="<?php echo e($service->duration); ?>">
                                                            <td>
                                                                <div class="card flex-row shadow-none p-0 gap-3">
                                                                    <div
                                                                        class="card-header p-0 border-0 align-items-start">
                                                                        <img src="<?php echo e(asset('website') . '/' . $service->image); ?>"
                                                                            class="h-80px w-80px rounded-3 object-fit-contain"
                                                                            alt="card-image" />
                                                                    </div>
                                                                    <div class="card-body p-0">
                                                                        <p class="fs-16 regular black">
                                                                            <?php echo e($service->name); ?></p>
                                                                        <p class="light-black opacity-6 fs-14 normal">
                                                                            <?php echo e($service->description); ?></p>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td><?php echo e($service->duration); ?> minutes</td>
                                                            <td>$<?php echo e($service->price); ?></td>
                                                            <td><?php echo e($service->category->name ?? 'N/A'); ?></td>
                                                            <td data-label="" class="text-end" data-bs-toggle="modal"
                                                                data-bs-target="#service-details"> <span
                                                                    class="gray-btn rounded-1 deep-blue fs-16 add-to-cart">
                                                                    <i class="fas fa-plus"></i> Add to cart
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                        <tr>
                                                            <td colspan="4" class="text-center py-4">
                                                                <p class="fs-16 text-muted">No services available</p>
                                                            </td>
                                                        </tr>
                                                    <?php endif; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <!-- Category Tabs Content -->
                                    <?php $__currentLoopData = $userCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="tab-pane fade" id="category-<?php echo e($category->id); ?>" role="tabpanel"
                                            aria-labelledby="category-<?php echo e($category->id); ?>-tab">
                                            <div class="d-flex justify-content-between flex-start flex-wrap">
                                                <!-- Subcategories for the specific category -->
                                                <ul class="nav nav-pills mb-10" class="pro-subcategories-tabs"
                                                    id="subcategory-pills-tab" role="tablist">
                                                    <?php if(isset($userSubcategoriesByCategory[$category->id])): ?>
                                                        <?php $__currentLoopData = $userSubcategoriesByCategory[$category->id]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <li class="nav-item" role="presentation">
                                                                <button
                                                                    class="nav-link <?php echo e($loop->first ? 'active' : ''); ?>"
                                                                    id="subcategory-<?php echo e($subcategory->id); ?>-tab"
                                                                    data-bs-toggle="pill"
                                                                    data-bs-target="#subcategory-<?php echo e($subcategory->id); ?>"
                                                                    type="button" role="tab"
                                                                    aria-controls="subcategory-<?php echo e($subcategory->id); ?>"
                                                                    aria-selected="<?php echo e($loop->first ? 'true' : 'false'); ?>">
                                                                    <?php echo e($subcategory->name); ?>

                                                                </button>
                                                            </li>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php else: ?>
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link active" type="button">
                                                                No subcategories available
                                                            </button>
                                                        </li>
                                                    <?php endif; ?>
                                                </ul>
                                            </div>

                                            <!-- Services under each subcategory -->
                                            <div class="tab-content" id="subcategory-pills-tabContent">
                                                <?php if(isset($userSubcategoriesByCategory[$category->id])): ?>
                                                    <?php $__currentLoopData = $userSubcategoriesByCategory[$category->id]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="tab-pane fade <?php echo e($loop->first ? 'show active' : ''); ?>"
                                                            id="subcategory-<?php echo e($subcategory->id); ?>" role="tabpanel"
                                                            aria-labelledby="subcategory-<?php echo e($subcategory->id); ?>-tab">
                                                            <div class="row row-gap-5">
                                                                <table id="responsiveTable"
                                                                    class="display wallet-history-table"
                                                                    style="width: 100%">
                                                                    <thead>
                                                                        <tr>
                                                                            <th></th>
                                                                            <th>Duration</th>
                                                                            <th>Price</th>
                                                                            <th>Category</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <?php
                                                                            // Get only this professional's services that belong to this subcategory
$professionalServicesInSubcategory = $user->services->where(
    'subcategory_id',
                                                                                $subcategory->id,
                                                                            );
                                                                        ?>
                                                                        <?php $__currentLoopData = $professionalServicesInSubcategory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <tr>
                                                                                <td>
                                                                                    <div
                                                                                        class="card flex-row shadow-none p-0 gap-3">
                                                                                        <div
                                                                                            class="card-header p-0 border-0 align-items-start">
                                                                                            <img src="<?php echo e(asset('website')); ?>/assets/images/service-image.png"
                                                                                                class="h-80px w-80px rounded-3 object-fit-contain"
                                                                                                alt="card-image" />
                                                                                        </div>
                                                                                        <div class="card-body p-0">
                                                                                            <p
                                                                                                class="light-black opacity-6 fs-14 normal">
                                                                                                <?php echo e($service->description); ?>

                                                                                            </p>
                                                                                        </div>
                                                                                    </div>
                                                                                </td>
                                                                                <td><?php echo e($service->duration); ?> minutes</td>
                                                                                <td>$<?php echo e($service->price); ?></td>
                                                                                <td><?php echo e($service->category->name); ?></td>
                                                                                <td data-label="" class="text-end"
                                                                                    data-bs-toggle="modal"
                                                                                    data-bs-target="#service-details">
                                                                                    <span
                                                                                        class="gray-btn rounded-1 deep-blue fs-16 add-to-cart">
                                                                                        <i class="fas fa-plus"></i> Add to
                                                                                        cart
                                                                                    </span>
                                                                                </td>
                                                                            </tr>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php else: ?>
                                                    <div class="tab-pane fade show active" role="tabpanel">
                                                        <div class="row row-gap-5">
                                                            <div class="col-12 text-center py-4">
                                                                <p class="fs-16 text-muted">No subcategories or services
                                                                    available for this category</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="container">
                        <div class="row">
                            <div class="col-md-12 mb-15">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5>Reviews</h5>
                                        <p class="black"> <i class="fas fa-star black"></i> 5.0 (546)</p>
                                    </div>
                                    
                                </div>
                                <div class="swiper mySwiper review-swiper">
                                    <div class="swiper-wrapper">
                                        <?php for($i = 0; $i <= 6; $i++): ?>
                                            <div class="swiper-slide">
                                                <div class="guarantee-section rounded-4 p-10">
                                                    <div class="d-flex" style="gap: 10px;">
                                                        <img src="<?php echo e(asset('website')); ?>/assets/images/family4.png"
                                                            alt="Reviewer Photo"
                                                            style="width: 48px; height: 48px; border-radius: 50%;">
                                                        <div>
                                                            <p class="fs-15 Sora bold mb-0">Charlie Culhane</p>
                                                            <p class="dark-cool-gray fs-14">Tue, 25 Mar 2025 at 5:12 pm</p>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <p class="fs-14">
                                                            Lorem ipsum dolor sit amet consectetur. Enim fames a pharetra
                                                            congue
                                                            non amet.
                                                            Amet ut a tellus ipsum volutpat eget velit nulla ridiculus. Eu
                                                            nunc
                                                            at aliquam
                                                            lorem leo sit.
                                                        </p>
                                                        <div>
                                                            <span> <i class="fas fa-star"></i> <i class="fas fa-star"></i>
                                                                <i class="fas fa-star"></i> <i class="fas fa-star"></i> <i
                                                                    class="fas fa-star"></i></span> <span
                                                                class="fs-14 bold">5.0</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endfor; ?>
                                        <!-- Add more .swiper-slide items here for additional reviews -->
                                    </div>
                                    <!-- Navigation buttons -->
                                    <div class="swiper-button-next"></div>
                                    <div class="swiper-button-prev"></div>
                                </div>
                            </div>

                            <?php if($user->staffs->count() > 0): ?>
                                <div class="col-md-12 mb-15">
                                    <div class="d-flex justify-content-between align-items-center mb-10">
                                        <h5>Meet The Team</h5>
                                        
                                    </div>

                                    <div class="swiper mySwiper meet-the-team-swiper">
                                        <div class="swiper-wrapper">
                                            <?php $__currentLoopData = $user->staffs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $staff): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="swiper-slide">
                                                    <div class="guarantee-section p-5">
                                                        <div class="d-flex flex-column justify-content-center align-items-center"
                                                            style="gap: 10px;">
                                                            <img src="<?php echo e(asset('website') . '/' . $staff->image); ?>"
                                                                alt="Reviewer Photo">
                                                            <p class="fs-16 bold mb-2"><?php echo e($staff->name ?? ''); ?></p>

                                                            <ul class="pro-social-icons">
                                                                <li> <img
                                                                        src="http://127.0.0.1:8000/website/assets/images/facebook-original.svg"
                                                                        class="img-fluid " alt="card-image"> </li>
                                                                <li> <img
                                                                        src="http://127.0.0.1:8000/website/assets/images/instagram-original.svg"
                                                                        class="img-fluid " alt="card-image"> </li>
                                                                <li> <img
                                                                        src="http://127.0.0.1:8000/website/assets/images/twitter-original.svg"
                                                                        class="img-fluid " alt="card-image"> </li>
                                                                <li> <img
                                                                        src="http://127.0.0.1:8000/website/assets/images/tiktok-original.svg"
                                                                        class="img-fluid " alt="card-image"> </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <!-- Add more .swiper-slide items here for additional reviews -->
                                        </div>

                                        <!-- Navigation buttons -->
                                        <div class="swiper-button-next"></div>
                                        <div class="swiper-button-prev"></div>
                                    </div>


                                </div>
                            <?php endif; ?>

                            

                            <?php if($user->certificates->count() > 0): ?>
                                <div class="col-md-12 mb-10">
                                    <div class="d-flex justify-content-between align-items-center mb-10">
                                        <h5 class="fs-24">Certifications & Licenses</h5>
                                        
                                    </div>

                                    <div class="swiper cert-swiper">
                                        <div class="swiper-wrapper">
                                            <?php $__currentLoopData = $user->certificates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $certificate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="swiper-slide">
                                                    <div class="guarantee-section p-5 ">
                                                        <div class=" certificate-license-swiper gap-5">
                                                            <img src="<?php echo e(asset('website') . '/' . $certificate->image); ?>"
                                                                alt="Reviewer Photo" s>

                                                            <p class="fs-14 bold mb-2"> <?php echo e($certificate->name ?? ''); ?>

                                                            </p>
                                                            <p class="link-gray fs-14 mb-2">Issued by: <span
                                                                    class="black fs-14">
                                                                    <?php echo e($certificate->issued_by ?? ''); ?></span></p>
                                                            <p class="link-gray fs-14 mb-2">Issue Date: <span
                                                                    class="black fs-14">
                                                                    <?php echo e($certificate->issued_date ?? ''); ?></span></p>
                                                            <p class="link-gray fs-14"> Expiry Date:<span
                                                                    class="black fs-14">
                                                                    <?php echo e($certificate->end_date ?? ''); ?></span></p>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <div class="swiper-button-next"></div>
                                        <div class="swiper-button-prev"></div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if($user->profile && $user->profile->lat && $user->profile->lng): ?>
                                <div class="col-md-12 mb-10" id="directions">
                                    <h5 class="fs-24 mb-8">Where to find us</h5>
                                    <!-- Google Map using existing include -->
                                    <div id="map" style="width:100%; height: 500px; border-radius: 8px;"></div>
                                    <!-- Hidden inputs for the map include -->
                                    <input type="hidden" id="pac-input" value="<?php echo e($user->profile->location); ?>"
                                        style="display: none;">
                                    <input type="hidden" id="latitude" value="<?php echo e($user->profile->lat); ?>">
                                    <input type="hidden" id="longitude" value="<?php echo e($user->profile->lng); ?>">
                                    <p class="fs-14 light-black pt-5">
                                        <?php echo e($user->profile->location ?? ($user->profile->city . ', ' . $user->profile->country ?? 'Location not specified')); ?>

                                        <span class="fs-14 ms-5 deep-blue cursor-pointer"
                                            onclick="getDirections(<?php echo e($user->profile->lat); ?>, <?php echo e($user->profile->lng); ?>)">
                                            <i class="fas fa-external-link-alt "> </i> Get Directions
                                        </span>
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 time-container">
                    <div class="guarantee-section mb-10 px-5 pt-10">
                        <?php if(auth()->check() && auth()->user()->hasRole('customer')): ?>
                            <a href="<?php echo e(route('chats.index', ['professional_id' => $user->ids])); ?>" class="blue-button d-block text-center">
                                <span> <i class="bi bi-chat-left-text text-chat"></i> </span> Message
                            </a>
                        <?php else: ?>
                            <a href="#!" class="blue-button d-block text-center">
                                <span> <i class="bi bi-chat-left-text text-chat"></i> </span> Message
                            </a>
                        <?php endif; ?>
                        <div class="hours-container mt-10">
                            <div class="hours-toggle"
                                onclick="document.querySelector('.hours-list').classList.toggle('active')">
                                <i class="far fa-clock large-icon"></i>
                                <span class="fs-14 Sora">Open until</span>
                                <span class="chevron"> <i class="fas fa-angle-up"></i></span>
                            </div>
                            <ul class="hours-list ">
                                <?php $__currentLoopData = $user->allOpeningHours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $openingHour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="hours-row">
                                        <span class="day"><i class="dot">●</i> <?php echo e($openingHour->day); ?></span>
                                        <?php if($openingHour->open && $openingHour->close): ?>
                                            <span class="time"><?php echo e($openingHour->open ?? 'Closed'); ?> –
                                                <?php echo e($openingHour->close ?? 'Closed'); ?></span>
                                        <?php else: ?>
                                            <span class="time">Closed</span>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>

                            <div class="pt-6">
                                <p class="fs-14 Sora light-black"> <span> <img
                                            src="<?php echo e(asset('website')); ?>/assets/images/phone-call.svg" class="img-fluid "
                                            alt="card-image"> </span> <?php echo e($user->profile->phone ?? ''); ?> </p>
                                <?php if($user->profile->city && $user->profile->country): ?>
                                    <p class="fs-14 Sora light-black"> <span> <i class="bi bi-geo-alt large-icon"></i>
                                        </span>
                                        <?php echo e($user->profile->city ?? ''); ?> , <?php echo e($user->profile->country ?? ''); ?> </p>
                                <?php endif; ?>
                                

                                <ul class="service-social-icons">
                                    <li> <img src="<?php echo e(asset('website')); ?>/assets/images/facebook-original.svg"
                                            class="img-fluid " alt="card-image"> </li>
                                    <li> <img src="<?php echo e(asset('website')); ?>/assets/images/instagram-original.svg"
                                            class="img-fluid " alt="card-image"> </li>
                                    <li> <img src="<?php echo e(asset('website')); ?>/assets/images/twitter-original.svg"
                                            class="img-fluid " alt="card-image"> </li>
                                    <li> <img src="<?php echo e(asset('website')); ?>/assets/images/tiktok-original.svg"
                                            class="img-fluid " alt="card-image"> </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php echo $__env->make('dashboard.templates.modal.add-service-details-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('dashboard.templates.modal.professional-services-filter', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('dashboard.templates.modal.professional-service-category-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php $__env->stopSection(); ?>

    <?php $__env->startPush('js'); ?>
        <script>
            var swiper = new Swiper(".review-swiper", {
                slidesPerView: 2,
                spaceBetween: 15,
                freeMode: true,
                loop: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
            });
        </script>

        <script>
            var swiper = new Swiper(".cert-swiper", {
                slidesPerView: 4,
                spaceBetween: 10,
                freeMode: true,
                loop: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
            });
        </script>


        <script>
            var swiper = new Swiper(".mySwiper", {
                slidesPerView: 3,
                spaceBetween: 10,
                freeMode: true,
                loop: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
            });
        </script>

        <script>
            var swiper = new Swiper(".mySwiper3", {
                slidesPerView: 5,
                spaceBetween: 20,
                freeMode: true,
                loop: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
            });
        </script>

        <script>
            var swiper = new Swiper(".mySwiper2", {
                slidesPerView: 14,
                spaceBetween: 20,
                // freeMode: true,
                loop: true,
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
            });
        </script>

        <script>
            function showMoreImages() {
                const hidden = document.querySelectorAll('.hidden-image');
                hidden.forEach(el => el.style.display = 'block');
                document.querySelector('.overlay').parentElement.remove();
            }
        </script>

        <script>
            function getDirections(lat, lng) {
                if (lat && lng) {
                    // Open Google Maps with directions
                    const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&travelmode=driving`;
                    window.open(directionsUrl, '_blank');
                } else {
                    alert('Location coordinates not available for this professional.');
                }
            }
        </script>
    <?php $__env->stopPush(); ?>

    <?php if($user->profile && $user->profile->lat && $user->profile->lng): ?>
        <?php echo $__env->make('layouts.includes.google-map', [
            'lat' => $user->profile->lat,
            'lng' => $user->profile->lng,
        ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <?php $__env->startPush('js'); ?>
        <script>
            $(document).ready(function() {
                // Handle category tab clicks
                $('.category-tab-btn').on('click', function() {
                    var categoryId = $(this).data('category-id');

                    // Remove active class from all category tabs (including All tab)
                    $('#all-services-tab, .category-tab-btn').removeClass('active').attr('aria-selected',
                        'false');
                    // Add active class to clicked tab
                    $(this).addClass('active').attr('aria-selected', 'true');

                    // Hide all tab panes
                    $('#pills-tabContent .tab-pane').removeClass('show active');

                    // Show the selected category tab pane
                    $('#category-' + categoryId).addClass('show active');

                    // Activate the first subcategory tab within this category
                    var firstSubcategoryTab = $('#category-' + categoryId + ' .nav-pills .nav-link').first();
                    var firstSubcategoryPane = $('#category-' + categoryId + ' .tab-content .tab-pane').first();

                    // Remove active from all subcategory tabs in this category
                    $('#category-' + categoryId + ' .nav-pills .nav-link').removeClass('active').attr(
                        'aria-selected', 'false');
                    $('#category-' + categoryId + ' .tab-content .tab-pane').removeClass('show active');

                    // Activate first subcategory
                    firstSubcategoryTab.addClass('active').attr('aria-selected', 'true');
                    firstSubcategoryPane.addClass('show active');
                });

                // Handle All tab click
                $('#all-services-tab').on('click', function() {
                    // Remove active class from all category tabs
                    $('.category-tab-btn').removeClass('active').attr('aria-selected', 'false');
                    // Add active class to All tab
                    $(this).addClass('active').attr('aria-selected', 'true');

                    // Hide all tab panes
                    $('#pills-tabContent .tab-pane').removeClass('show active');
                    // Show All services tab pane
                    $('#all-services').addClass('show active');
                });

            });
        </script>
        <script>
            $(document).ready(function() {
                $('.main-heading-icon').click(function() {
                    var userId = $(this).data('user-id');
                    var heartIcon = $('#heart-icon');

                    $.ajax({
                        url: '<?php echo e(route('favorite_professionals')); ?>',
                        type: 'POST',
                        data: {
                            _token: '<?php echo e(csrf_token()); ?>',
                            professional: userId
                        },
                        success: function(response) {
                            if (response.status === 'success') {
                                if (response.action === 'added') {
                                    // Change to filled red heart
                                    heartIcon.removeClass('far').addClass('fas text-danger');
                                } else if (response.action === 'removed') {
                                    // Change to outline heart
                                    heartIcon.removeClass('fas text-danger').addClass('far');
                                }
                            }
                        },
                        error: function(xhr, status, error) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: 'Something went wrong. Please try again.',
                                showConfirmButton: true
                            });
                        }
                    });
                });

                // Handle Generate/Copy Short URL button
                $('#generateShortUrlBtn').on('click', function() {
                    const userId = $(this).data('user-id');
                    const $btn = $(this);
                    const originalText = $btn.html();

                    // Show loading state
                    $btn.prop('disabled', true).html(
                        '<span class="spinner-border spinner-border-sm me-1"></span>Loading...');

                    $.ajax({
                        url: '<?php echo e(route('generate_short_url')); ?>',
                        method: 'POST',
                        data: {
                            _token: '<?php echo e(csrf_token()); ?>',
                            user_id: userId
                        },
                        success: function(response) {
                            if (response.success) {
                                // Copy to clipboard
                                navigator.clipboard.writeText(response.short_url).then(function() {
                                    // Show success message
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Success!',
                                        text: 'Short URL copied to clipboard!',
                                        timer: 2000,
                                        showConfirmButton: false
                                    });

                                    // Update button text
                                    $btn.html(
                                        '<i class="bi bi-link-45deg me-1"></i>Copy Short URL'
                                        );
                                }).catch(function() {
                                    // Fallback: show URL in alert if clipboard fails
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Short URL Generated!',
                                        html: `Your short URL: <br><strong>${response.short_url}</strong><br><small>Please copy it manually</small>`,
                                        showConfirmButton: true
                                    });

                                    // Update button text
                                    $btn.html(
                                        '<i class="bi bi-link-45deg me-1"></i>Copy Short URL'
                                        );
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error!',
                                    text: response.message ||
                                        'Failed to generate short URL',
                                    showConfirmButton: true
                                });
                            }
                        },
                        error: function(xhr) {
                            let errorMessage = 'An error occurred';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            }

                            Swal.fire({
                                icon: 'error',
                                title: 'Error!',
                                text: errorMessage,
                                showConfirmButton: true
                            });
                        },
                        complete: function() {
                            // Reset button state
                            $btn.prop('disabled', false);
                            if ($btn.html().includes('Loading')) {
                                $btn.html(originalText);
                            }
                        }
                    });
                });
            });
        </script>
    <?php $__env->stopPush(); ?>

<?php echo $__env->make(auth()->check() ? (auth()->user()->hasRole('customer') ? 'website.layout.master' : 'dashboard.layout.master') : 'website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/customer/professional_profile.blade.php ENDPATH**/ ?>
<?php $__env->startPush('js'); ?>
    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA1Qkj7ocEmNs4U5-T2vVJ04OrsCRH0tCU&libraries=places&v=weekly"
        async defer></script>
    <script>
        // Make initMap globally accessible
        window.initMap = function() {
            console.log('Attempting to initialize Google Maps...');

            // Check if required elements exist before initializing
            const mapElement = document.getElementById("map");
            const inputElement = document.getElementById("pac-input");
            const latElement = document.getElementById("latitude");
            const lngElement = document.getElementById("longitude");

            if (!mapElement || !inputElement || !latElement || !lngElement) {
                console.warn('Google Maps: Required elements not found', {
                    map: !!mapElement,
                    input: !!inputElement,
                    latitude: !!latElement,
                    longitude: !!lngElement
                });
                return;
            }

            // Check if Google Maps API is loaded
            if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
                console.warn('Google Maps API not loaded yet, retrying...');
                setTimeout(window.initMap, 500);
                return;
            }

            console.log('Google Maps: All elements found, initializing map...');

            // Check if we have existing coordinates
            const hasExistingCoords = <?php echo e(isset($lat) && isset($lng) && $lat && $lng ? 'true' : 'false'); ?>;
            const defaultLatLng = {
                lat: <?php echo e($lat ?? '40.7128'); ?>,
                lng: <?php echo e($lng ?? '-74.0060'); ?>

            };

            console.log('Google Maps Debug:', {
                hasExistingCoords: hasExistingCoords,
                lat: <?php echo e($lat ?? 'null'); ?>,
                lng: <?php echo e($lng ?? 'null'); ?>,
                defaultLatLng: defaultLatLng
            });

            const input = document.getElementById("pac-input");
            const searchBox = new google.maps.places.SearchBox(input);
            const geocoder = new google.maps.Geocoder();

            let map, marker;

            // Set placeholder text and hide map initially if no coordinates
            if (!hasExistingCoords) {
                console.log('No existing coordinates - showing placeholder');
                input.placeholder = "Please enter your location";
                mapElement.style.display = 'none';
                // Show a placeholder message
                const placeholder = document.createElement('div');
                placeholder.id = 'map-placeholder';
                placeholder.style.cssText = 'padding: 60px 40px; text-align: center; background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; color: #9ca3af; font-size: 16px; height: 400px; display: flex; flex-direction: column; justify-content: center; align-items: center;';
                placeholder.innerHTML = '<div style="color: #020C87; font-size: 32px; margin-bottom: 15px;">📍</div><div style="color: #9ca3af; font-size: 16px;">Map will appear here after you enter your location</div>';
                mapElement.parentNode.insertBefore(placeholder, mapElement);
                console.log('Placeholder created and inserted');
            } else {
                console.log('Has existing coordinates - initializing map immediately');
                // Initialize map immediately if we have coordinates
                initializeMap();
            }

            function initializeMap() {
                // Remove placeholder if it exists
                const placeholder = document.getElementById('map-placeholder');
                if (placeholder) {
                    placeholder.remove();
                }

                // Show map
                mapElement.style.display = 'block';

                map = new google.maps.Map(mapElement, {
                    center: defaultLatLng,
                    zoom: 14,
                    mapTypeControl: false,
                    streetViewControl: false,
                    rotateControl: true,
                });

                marker = new google.maps.Marker({
                    map,
                    position: defaultLatLng,
                    draggable: true,
                });

                // Only update fields if we have existing coordinates
                if (hasExistingCoords) {
                    updateLocationFields(defaultLatLng.lat, defaultLatLng.lng);
                    updateAddressFromLatLng(defaultLatLng.lat, defaultLatLng.lng);
                }

                // Add marker drag listener
                marker.addListener("dragend", function(event) {
                    const lat = event.latLng.lat();
                    const lng = event.latLng.lng();
                    updateLocationFields(lat, lng);
                    updateAddressFromLatLng(lat, lng);
                });
            }

            // On place search
            searchBox.addListener("places_changed", () => {
                const places = searchBox.getPlaces();
                if (!places.length || !places[0].geometry?.location) return;

                // Initialize map if not already initialized
                if (!map) {
                    initializeMap();
                }

                const location = places[0].geometry.location;

                // Update default coordinates for the map
                defaultLatLng.lat = location.lat();
                defaultLatLng.lng = location.lng();

                marker.setPosition(location);
                map.setCenter(location);
                map.setZoom(18);

                const lat = location.lat();
                const lng = location.lng();
                updateLocationFields(lat, lng);
                input.value = places[0].formatted_address || ""; // Directly update from place
            });

            function updateLocationFields(lat, lng) {
                document.getElementById("latitude").value = lat;
                document.getElementById("longitude").value = lng;
            }

            function updateAddressFromLatLng(lat, lng) {
                geocoder.geocode({
                    location: {
                        lat,
                        lng
                    }
                }, (results, status) => {
                    if (status === "OK" && results[0]) {
                        input.value = results[0].formatted_address;
                    } else {
                        input.value = ""; // Clear if not found
                        console.warn("Geocoder failed due to: " + status);
                    }
                });
            }

            console.log('Google Maps: Initialization completed successfully');
        };

        // Auto-initialize when Google Maps API loads (for non-modal pages)
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
                window.initMap();
            }
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\SALMAN\git\anders\resources\views/layouts/includes/google-map.blade.php ENDPATH**/ ?>
@extends('layouts.app')
@push('css')
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@1.5.2/dist/select2-bootstrap4.min.css"
        rel="stylesheet" />
    <style>
        .error-input {
            border-color: #dc3545 !important;
            outline: none !important;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25) !important;
        }

        .image-input.error-input {
            border-color: #dc3545 !important;
        }

        .image-input.error-input .image-input-wrapper {
            border-color: #dc3545 !important;
        }
    </style>
@endpush
@section('content')
    <div class="container professional-acc-form customer-registration">
        <div class="row justify-content-center">
            <div class="col-md-12 stepper-navigation">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <!-- Home Logo -->
                    <a href="{{ url('/') }}" class="home-logo">
                        <img src="{{ asset('website') . '/' . setting()->logo }}" alt="Home" class="img-fluid"
                            style="height: 60px;">
                    </a>

                    <!-- Continue Button with Dropdown -->

                    <div class="d-flex flex-column">
                        <a class="blue-border-btn mb-3" href="#" id="logoutBtn">Logout</a>
                        <button type="button" class="btn action-button next" id="continueBtn">Continue</button>
                    </div>


                    <!-- <div class="btn-group" role="group">
                                        <button type="button" class="btn action-button dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                            <span class="visually-hidden">Toggle Dropdown</span>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li></li>
                                        </ul>
                                    </div> -->
                </div>
            </div>

            <div class="col-md-8 mb-2 pt-20 mt-20">
                <div class=" px-0 pt-4 pb-0 mt-3 mb-3">
                    <form id="acc-form" action="{{ route('register.customer') }}" method="post"
                        enctype="multipart/form-data">
                        @csrf
                        <div class="form-card frst-step">
                            <div class="container">
                                <div class="row">
                                    <div class="col-md-12 mb-10">
                                        <h2>Create a Customer Account</h2>
                                        <p>You're almost there! Create your new account for {{ auth()->user()->email }} by
                                            completing these details.</p>
                                    </div>

                                    <div class="col-md-12 mb-10">
                                        <div class="Image-input_holder pro-account-image-holder mb-10">
                                            <div class="image-input image-input-empty" data-kt-image-input="true">
                                                <div class="image-input " data-kt-image-input="true">
                                                    <div class="image-input-wrapper"></div>
                                                    <label class="dark-green-btn fs-14 regular pt-9"
                                                        data-kt-image-input-action="change">
                                                        <span class="pe-3 fs-16 fw-600 mb-10 blue-text"> Upload Profile
                                                            Image<span style="color: red;">*</span></span>
                                                        <input type="file" name="avatar" id="avatar"
                                                            accept=".png, .jpg, .jpeg" id="profileImage" />
                                                        <input type="hidden" name="profile_image" />
                                                        <p class="fs-24 medium pt-4 gray-text"> At least 125x125 px
                                                            recommended. JPG or PNG is allowed</p>
                                                    </label>

                                                    <a href="#!" class="light-green-btn fs-14 regular ms-5"
                                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                        data-bs-dismiss="click" title="Cancel avatar"> <i
                                                            class="fas fa-times fa-5"></i> </a>

                                                    <a href="#!" class=" ms-5" data-kt-image-input-action="remove"><i
                                                            class="fas fa-trash-alt"></i> </a>
                                                </div>
                                            </div>
                                            <p class="image-error-msg mt-5" style="color: red; display: none;">Image
                                                required</p>
                                            @error('avatar')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-12 mb-6">
                                        <label for="fullname" class="fieldlabels">Full Name <span
                                                style="color: red;">*</span></label>
                                        <input type="text" name="fullname" id="fullname" value="{{ old('fullname') }}"
                                            placeholder="Enter your first name" id="fullname" />
                                        @error('fullname')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    <div class="col-md-12">
                                        <label for="email" class="fieldlabels">Email Address </label>
                                        <input type="email" value="{{ auth()->user()->email }}" id="email" readonly />
                                        @error('email')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    <div class="col-md-12">
                                        <label for="phone" class="fieldlabels">Phone Number <span
                                                style="color: red;">*</span> </label>
                                        <input class="w-100 " type="tel" value="{{ old('phone') }}" id="phone"
                                            name="phone">
                                        @error('phone')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    <div class="mb-3 service_preferances">
                                        <label for="services" class="form-label fw-semibold">
                                            Services Preferences <span class="text-danger">*</span>
                                        </label>
                                        <select id="services" name="services[]" class="form-select select2" multiple>
                                            @foreach ($services as $service)
                                                <option value="{{ $service->id }}"
                                                    {{ is_array(old('services')) && in_array($service->id, old('services')) ? 'selected' : '' }}>
                                                    {{ $service->name }}
                                                </option>
                                            @endforeach
                                        </select>

                                        @error('services')
                                            <div class="text-danger mt-1">
                                                <small><strong>{{ $message }}</strong></small>
                                            </div>
                                        @enderror
                                    </div>

                                    {{-- <div class="col-md-12 position-relative mb-6">
                                        <label for="website" class="fieldlabels">Password <span style="color: red;">*</span> </label>
                                        <input type="password" class="form-control" name="password"
                                            placeholder="Enter password" id="password" />
                                        @error('password')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror

                                          <span id="toggle-password"
                                            class=" btn-sm btn-icon 2">
                                            <i class="fa-solid fa-eye pass-icon"></i>
                                            <i class="fa-solid fa-eye-slash pass-icon d-none "></i>
                                        </span>
                                    </div>

                                    <div class="col-md-12 position-relative mb-6">
                                        <label for="facebook" class="fieldlabels">Confirm Password <span style="color: red;">*</span> </label>
                                        <input type="password" class="form-control" name="confirm_password"
                                            placeholder="Confirm your password" id="confirm_password" />
                                        @error('confirm_password')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                        <span id="toggle-password-confirm"
                                            class="btn-sm btn-icon">
                                            <i class="fa-solid fa-eye pass-icon"></i>
                                            <i class="fa-solid fa-eye-slash pass-icon d-none"></i>
                                        </span>
                                    </div> --}}

                                    <div class="col-md-12 customer-loc-map mb-6 mt-4">
                                        <label for="location" class="fieldlabels w-100">Location <span
                                                style="color: red;">*</span></label>
                                        <input id="pac-input" type="text" name="location"
                                            value="{{ old('location', auth()->user()->profile?->location ?? '') }}"
                                            placeholder="Please enter your location" />
                                        <input type="hidden" name="lat"
                                            value="{{ old('lat', auth()->user()->profile?->lat ?? '') }}" id="latitude">
                                        <input type="hidden" name="lng"
                                            value="{{ old('lng', auth()->user()->profile?->lng ?? '') }}" id="longitude">

                                        <!-- Map Container -->
                                        <div  id="map">  </div>
                                        @error('location')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://ajax.microsoft.com/ajax/jquery.validate/1.7/additional-methods.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@17.0.19/build/js/intlTelInput.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>



    <script>
        $('#toggle-password').on('click', function() {
            var passwordField = $('#password');
            var passwordFieldType = passwordField.attr('type');
            if (passwordFieldType === 'password') {
                passwordField.attr('type', 'text');
                $(this).find('.fa-eye-slash').removeClass('d-none');
                $(this).find('.fa-eye').addClass('d-none');
            } else {
                passwordField.attr('type', 'password');
                $(this).find('.fa-eye').removeClass('d-none');
                $(this).find('.fa-eye-slash').addClass('d-none');
            }
        });
        $('#toggle-password-confirm').on('click', function() {
            var confirmPasswordField = $('#confirm_password');
            var confirmPasswordFieldType = confirmPasswordField.attr('type');
            if (confirmPasswordFieldType === 'password') {
                confirmPasswordField.attr('type', 'text');
                $(this).find('.fa-eye-slash').removeClass('d-none');
                $(this).find('.fa-eye').addClass('d-none');
            } else {
                confirmPasswordField.attr('type', 'password');
                $(this).find('.fa-eye').removeClass('d-none');
                $(this).find('.fa-eye-slash').addClass('d-none');
            }
        });
    </script>
    <script>
        const input = document.querySelector("#phone");
        const iti = window.intlTelInput(input, {
            initialCountry: "auto",
            geoIpLookup: function(callback) {
                fetch("https://ipinfo.io/json?token=1e240fc8539ff6")
                    .then((resp) => resp.json())
                    .then((resp) => {
                        const countryCode = resp && resp.country ? resp.country : "us";
                        callback(countryCode);
                    })
                    .catch(() => callback("us")); // fallback
            },
            utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js"
        });

        // Add phone validation method to jQuery validator
        $.validator.addMethod("phoneValidation", function(value, element) {
            if (this.optional(element)) {
                return true;
            }
            return iti.isValidNumber();
        }, "Please enter a valid phone number");

        // Real-time validation on phone input
        input.addEventListener('blur', function() {
            const phoneError = document.getElementById('phone-error');
            if (phoneError) {
                phoneError.remove();
            }

            if (input.value.trim() && !iti.isValidNumber()) {
                // Create error message element
                const errorElement = document.createElement('span');
                errorElement.id = 'phone-error';
                errorElement.className = 'invalid-feedback';
                errorElement.style.display = 'block';
                errorElement.style.color = 'red';
                errorElement.style.fontWeight = 'bold';
                errorElement.innerHTML = '<strong>Please enter a valid phone number</strong>';

                // Insert after phone input
                input.parentNode.appendChild(errorElement);
                input.classList.add('error-input');
            } else {
                input.classList.remove('error-input');
            }
        });

        // Clear error on input change
        input.addEventListener('input', function() {
            const phoneError = document.getElementById('phone-error');
            if (phoneError) {
                phoneError.remove();
            }
            input.classList.remove('error-input');
        });
    </script>

    <script>
        const fileInput = document.querySelector('input[type="file"]');
        const wrapper = document.querySelector('.image-input-wrapper');
        const removeBtn = document.querySelector('[data-kt-image-input-action="remove"]');
        const cancelBtn = document.querySelector('[data-kt-image-input-action="cancel"]');
        const imageInput = document.querySelector('.image-input');

        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];

            if (file) {
                const reader = new FileReader();

                reader.onload = function(event) {
                    wrapper.style.backgroundImage = `url('${event.target.result}')`;
                    imageInput.classList.remove('image-input-empty');
                };

                reader.readAsDataURL(file);
            }
        });

        // Remove action
        removeBtn.addEventListener('click', function() {
            wrapper.style.backgroundImage = '';
            fileInput.value = '';
            imageInput.classList.add('image-input-empty');
            // Set hidden input for backend if needed
            document.querySelector('input[name="avatar_remove"]').value = '1';
        });

        // Optional: Cancel action (if you need to reset back to default image, add that logic)
        cancelBtn.addEventListener('click', function() {
            wrapper.style.backgroundImage = '';
            fileInput.value = '';
            imageInput.classList.add('image-input-empty');
        });
    </script>

    <script>
        $(document).ready(function() {
            // Initialize Select2 for services with Bootstrap 4 theme
            $('#services').select2({
                theme: 'bootstrap4',
                placeholder: "Select services",
                allowClear: false,
                width: '100%'
            });

            // Trigger validation when Select2 changes
            $('#services').on('change', function() {
                $(this).valid();
            });

            $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                if (element.files.length === 0) {
                    return true; // no file selected, let 'required' rule handle this
                }
                const fileSizeKB = element.files[0].size / 1024; // size in KB
                return fileSizeKB <= maxSizeKB;
            }, 'File size must be less than {0} KB.');

            // Add custom regex validation method (if not already included)
            $.validator.addMethod("pattern", function(value, element, param) {
                if (this.optional(element)) {
                    return true;
                }
                if (typeof param === "string") {
                    param = new RegExp(param);
                }
                return param.test(value);
            }, "Invalid format");

            // Add custom password complexity validator matching your regex
            $.validator.addMethod("passwordComplexity", function(value, element) {
                return this.optional(element) ||
                    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,16}$/.test(value);
            }, "Password must include at least 1 uppercase, 1 lowercase, 1 number, and 1 special character");

            $("#acc-form").validate({
                ignore: ':hidden:not(select)', // Don't ignore hidden selects (for Select2)
                rules: {
                    fullname: {
                        required: true,
                        maxlength: 30,
                        pattern: /^[a-zA-Z0-9\s]+$/
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    phone: {
                        required: true,
                        phoneValidation: true
                    },
                    "services[]": {
                        required: true,
                        minlength: 1
                    },
                    // password: {
                    //     required: true,
                    //     minlength: 8,
                    //     maxlength: 16,
                    //     passwordComplexity: true
                    // },
                    // confirm_password: {
                    //     required: true,
                    //     equalTo: "#password"
                    // },
                    avatar: {
                        required: true,
                        maxFileSize: 5120
                    },
                    location: {
                        required: true
                    },
                    lat: {
                        required: true,
                        numeric: true
                    },
                    lng: {
                        required: true,
                        numeric: true
                    }
                },
                messages: {
                    fullname: {
                        required: "Please enter your full name",
                        maxlength: "Your full name must not exceed 30 characters",
                        pattern: "Full name can only contain letters, numbers, and spaces"
                    },
                    email: {
                        required: "Please enter your email address",
                        email: "Please enter a valid email address"
                    },
                    phone: {
                        required: "Please enter your phone number",
                        phoneValidation: "Please enter a valid phone number"
                    },
                    "services[]": {
                        required: "Please select at least one service preference",
                        minlength: "Please select at least one service preference"
                    },
                    // password: {
                    //     required: "Please enter your password",
                    //     minlength: "Your password must consist of at least 8 characters",
                    //     maxlength: "Your password must not exceed 16 characters",
                    //     passwordComplexity: "Password must include at least 1 uppercase, 1 lowercase, 1 number, and 1 special character"
                    // },
                    // confirm_password: {
                    //     required: "Please confirm your password",
                    //     equalTo: "Please enter the same password as above"
                    // },
                    avatar: {
                        required: "Please upload your profile image",
                        maxFileSize: "Image size must not exceed 5 MB"
                    },
                    location: {
                        required: "Please select your location"
                    },
                    lat: {
                        required: "Please select your location",
                        numeric: "Please select your location"
                    },
                    lng: {
                        required: "Please select your location",
                        numeric: "Please select your location"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                }
            });


            $(document).on('click', '#continueBtn', function(e) {
                e.preventDefault();
                $("#acc-form").submit();
            });

            // Handle logout with confirmation
            $(document).on('click', '#logoutBtn', function(e) {
                e.preventDefault();

                Swal.fire({
                    title: 'Registration Not Complete',
                    text: 'Your registration is not complete yet. You can continue from here anytime. Are you sure you want to logout?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, Logout',
                    cancelButtonText: 'Continue Registration'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = "{{ url('logout') }}";
                    }
                });
            });
        });
    </script>

    <!-- Google Maps Script -->
    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA1Qkj7ocEmNs4U5-T2vVJ04OrsCRH0tCU&libraries=places&v=weekly"
        async defer></script>
    <script>
        // Initialize Google Maps for Customer Registration
        function initCustomerMap() {
            console.log('Initializing Customer Registration Map...');

            const mapElement = document.getElementById("map");
            const inputElement = document.getElementById("pac-input");
            const latElement = document.getElementById("latitude");
            const lngElement = document.getElementById("longitude");

            if (!mapElement || !inputElement || !latElement || !lngElement) {
                console.warn('Required map elements not found');
                return;
            }

            // Check if Google Maps API is loaded
            if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
                console.warn('Google Maps API not loaded yet, retrying...');
                setTimeout(initCustomerMap, 500);
                return;
            }

            // Get existing coordinates
            const existingLat = '{{ old('lat', auth()->user()->profile?->lat ?? '') }}';
            const existingLng = '{{ old('lng', auth()->user()->profile?->lng ?? '') }}';
            const hasExistingCoords = existingLat && existingLng && existingLat !== '' && existingLng !== '';

            console.log('Map Debug:', {
                hasExistingCoords: hasExistingCoords,
                lat: existingLat,
                lng: existingLng
            });

            const defaultLatLng = {
                lat: hasExistingCoords ? parseFloat(existingLat) : 40.7128,
                lng: hasExistingCoords ? parseFloat(existingLng) : -74.0060
            };

            const searchBox = new google.maps.places.SearchBox(inputElement);
            const geocoder = new google.maps.Geocoder();
            let map, marker;

            // Show placeholder if no coordinates
            if (!hasExistingCoords) {
                console.log('No coordinates - showing placeholder');
                inputElement.placeholder = "Please enter your location";
                mapElement.style.display = 'none';

                // Create placeholder
                const placeholder = document.createElement('div');
                placeholder.id = 'map-placeholder';
                placeholder.style.cssText =
                    'padding: 60px 40px; text-align: center; background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; color: #9ca3af; font-size: 16px; height: 400px; display: flex; flex-direction: column; justify-content: center; align-items: center;';
                placeholder.innerHTML =
                    '<div style="color: #020C87; font-size: 32px; margin-bottom: 15px;">📍</div><div style="color: #9ca3af; font-size: 16px;">Map will appear here after you enter your location</div>';
                mapElement.parentNode.insertBefore(placeholder, mapElement);
                console.log('Placeholder created');
            } else {
                console.log('Has coordinates - showing map');
                initializeMap();
            }

            function initializeMap() {
                // Remove placeholder if exists
                const placeholder = document.getElementById('map-placeholder');
                if (placeholder) {
                    placeholder.remove();
                }

                // Show map
                mapElement.style.display = 'block';

                map = new google.maps.Map(mapElement, {
                    center: defaultLatLng,
                    zoom: 14,
                    mapTypeControl: false,
                    streetViewControl: false,
                    rotateControl: true,
                });

                marker = new google.maps.Marker({
                    map,
                    position: defaultLatLng,
                    draggable: true,
                });

                // Update fields if we have existing coordinates
                if (hasExistingCoords) {
                    updateLocationFields(defaultLatLng.lat, defaultLatLng.lng);
                    updateAddressFromLatLng(defaultLatLng.lat, defaultLatLng.lng);
                }

                // Add marker drag listener
                marker.addListener("dragend", function(event) {
                    const lat = event.latLng.lat();
                    const lng = event.latLng.lng();
                    updateLocationFields(lat, lng);
                    updateAddressFromLatLng(lat, lng);
                });
            }

            // On place search
            searchBox.addListener("places_changed", () => {
                const places = searchBox.getPlaces();
                if (!places.length || !places[0].geometry?.location) return;

                // Initialize map if not already initialized
                if (!map) {
                    initializeMap();
                }

                const location = places[0].geometry.location;
                marker.setPosition(location);
                map.setCenter(location);
                map.setZoom(18);

                const lat = location.lat();
                const lng = location.lng();
                updateLocationFields(lat, lng);
                inputElement.value = places[0].formatted_address || "";
            });

            function updateLocationFields(lat, lng) {
                document.getElementById("latitude").value = lat;
                document.getElementById("longitude").value = lng;
            }

            function updateAddressFromLatLng(lat, lng) {
                geocoder.geocode({
                    location: {
                        lat,
                        lng
                    }
                }, (results, status) => {
                    if (status === "OK" && results[0]) {
                        inputElement.value = results[0].formatted_address;
                    }
                });
            }

            console.log('Customer Map initialization completed');
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Small delay to ensure all elements are ready
            setTimeout(initCustomerMap, 100);
        });

        // Also try to initialize when Google Maps loads
        window.initMap = initCustomerMap;
    </script>
@endpush

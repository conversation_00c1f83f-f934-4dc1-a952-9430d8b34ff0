<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\CmsPage;
use App\Models\Home;
use App\Models\PrivacyAndTerm;
use App\Models\Service;
use App\Models\User;
use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class WebsiteController extends Controller
{
    function __construct()
    {
        $page = CmsPage::all();
        view()->share('pages', $page);
    }
    public function index()
    {
        $page = Home::with('details')->first();
        $categories = Category::where('status', 1)->get();
        $services = Service::active()->get();
        $professionals = User::professional()->get();
        return view('website.index', compact('page', 'categories', 'professionals'));
    }

    public function services($category = null, $subcategory = null)
    {
        $categories = Category::wherehas('subcategories')->active()->get();
        $active_category = $category;
        $active_subcategory = $subcategory;
        $services = Service::get();
        return view('website.service', compact('services', 'categories', 'active_category', 'active_subcategory'));
    }
    public function filterServices(Request $request)
    {
        // try {
        if (!$request->subcategory && !$request->category) {
            $categories = Category::wherehas('subcategories')->active()->get();
            $subcategories = $categories->first()->subcategories;
            $selectedSubcategory = $subcategories->first();
            $services = Service::get();
        } else {
            $categories = Category::with("subcategories", "services")->where("slug", $request->category)->active()->first();
            $subcategories = $categories->subcategories;
            if ($request->has('subcategory')) {
                $selectedSubcategory = $subcategories->where('slug', $request->subcategory)->first();
            } else {
                $selectedSubcategory = $subcategories->first();
            }
            $services = Service::where('category_id', $categories->id)->where('subcategory_id', $selectedSubcategory->id)->get();
        }

        $data = view('website.template.subcategory-service', compact('categories', 'subcategories', 'services', 'selectedSubcategory'))->render();
        return api_response(true, "Services", [
            'page' => $data,
            'category' => $request->category ?? '',
            'subcategory' => $selectedSubcategory->slug ?? ''
        ]);
        // } catch (\Exception $e) {
        //     return api_response(false, "Something went wrong");
        // }
    }
    public function getServiceDetails(Request $request, $id)
    {
        $service = Service::where('ids', $id)->with('availabilities')->firstOrFail();
        $duration = $service->duration;
        $timeSlots = $service->availabilitiesOnDate($request->date)->get(['start_time', 'end_time'])->map(function ($slot) use ($duration) {
            return [
                'start_time' => $slot->start_time,
                'end_time'   => $slot->end_time,
                'duration'   => $duration,
            ];
        });
        return response()->json([
            'status' => true,
            'message' => 'Service Details',
            'data' => view('website.template.service-details', compact('service'))->render(),
            'services' => $service,  // raw service data sent separately
            'slots' => $timeSlots  // raw service data sent separately
        ]);
    }
    public function getById($id, $service_id = null)
    {
        $services = Service::where('user_id', $id)->get();
        $service_id = $service_id ?? $services->first()->ids;
        $html = view('dashboard.templates.modal.professional.add-professional-services', compact('services', 'service_id'))->render();

        return response()->json([
            'html' => $html,
            'service_id' => $service_id
        ]);
    }
    public function getServiceTimeSlots(Request $request, $id)
    {
        $service = Service::where('ids', $id)->with('availabilities')->firstOrFail();
        $duration = $service->duration;
        $date = $request->get('date', now()->format('Y-m-d'));

        $timeSlots = $service->availabilitiesOnDate($date)->get(['start_time', 'end_time'])->map(function ($slot) use ($duration) {
            return [
                'start_time' => $slot->start_time,
                'end_time'   => $slot->end_time,
                'duration'   => $duration,
            ];
        });
        $bookedSlots = Booking::where('service_id', $service->id)->where('booking_date', $date)->pluck('booking_time');
        return response()->json([
            'status' => true,
            'message' => 'Time Slots Retrieved',
            'slots' => $timeSlots,
            'date' => $date,
            'bookedSlots' => $bookedSlots
        ]);
    }

    public function getServiceAvailabiliesDetails($id)
    {
        $service = Service::where('ids', $id)->firstOrFail();

        return api_response(true, "Service Details", view('website.template.service-details', compact('service'))->render());
    }
    public function getfamilyDetails($type)
    {
        $friends  = auth()->user()->friends;
        return api_response(true, "Service Details", view('website.template.related-friends', compact('friends'))->render());
    }

    public function professional($category = null, $subcategory = null)
    {
        $categories = Category::wherehas('subcategories')->active()->get();
        $active_category = $category;
        $active_subcategory = $subcategory;
        $professionals = User::professional()->get();
        return view('website.professional', compact('professionals', 'categories', 'active_category', 'active_subcategory'));
    }

    public function filterProfessional(Request $request)
    {
        try {
            if (!$request->subcategory && !$request->category) {
                $categories = Category::wherehas('subcategories')->active()->get();
                $subcategories = $categories->first()->subcategories;
                $professionals = User::professional()->get();
            } else {
                $categories = Category::with("subcategories", "services")->where("slug", $request->category)->active()->first();
                $subcategories = $categories->subcategories;
                if ($request->has('subcategory')) {
                    $selectedSubcategory = $subcategories->where('slug', $request->subcategory)->first();
                } else {
                    $selectedSubcategory = $subcategories->first();
                }
                $professionals = $selectedSubcategory->users;
            }
            $data = view('website.template.subcategory-professional', compact('categories', 'subcategories', 'professionals'))->render();
            return api_response(true, "Professionals", [
                'page' => $data,
                'category' => $request->category ?? '',
                'subcategory' => $selectedSubcategory->slug ?? ''
            ]);
        } catch (\Exception $e) {
            return api_response(false, "Something went wrong");
        }
    }
    public function privacyPolicy()
    {
        $policies = PrivacyAndTerm::where('type', 'privacy')->get();
        return view('website.privacy-policy', compact('policies'));
    }
    public function terms()
    {
        $terms = PrivacyAndTerm::where('type', 'term')->get();
        return view('website.term', compact('terms'));
    }

    public function clear_all()
    {
        Artisan::call('route:clear');
        Artisan::call('cache:clear');
        Artisan::call('optimize:clear');
        Artisan::call('view:clear');
        Artisan::call('config:clear');
        return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Go to home</a></h4></div>';
    }

    public function showPage($slug)
    {
        $page = CmsPage::where('slug', $slug)->first();
        $decodedSnippet = html_entity_decode($page->code_snippets);
        if (!$page) {
            return redirect()->route('home');
        }
        return view('website.show', compact('page', 'decodedSnippet'));
    }
    public function searchTreatments(Request $request)
    {
        if ($request->filled('date') && $request->filled('time')) {
            $searchDate = Carbon::parse($request->date);
            $searchTime = $request->time;

            // Condition 1: If date is today and time has passed, return no results
            if ($searchDate->isToday()) {
                $searchDateTime = Carbon::parse($request->date . ' ' . $searchTime);
                if ($searchDateTime->isPast()) {
                    return view('website.search-results', ['services' => collect(), 'message' => 'Selected time has already passed.']);
                }
            }
        }
        $query = Service::query();
        // Apply search filter
        if ($request->filled('search')) {
            $query->where('name', 'LIKE', '%' . $request->search . '%');
        }
        // Apply location filter
        if ($request->filled('lat') && $request->filled('lng')) {
            $query->nearLocation($request->lat, $request->lng, 4);
        }
        $services = $query->get();

        // Apply date and time filters if provided
        if ($request->filled('date') && $request->filled('time')) {
            $searchDate = $request->date;
            $searchTime = Carbon::parse($request->time)->format('H:i:s'); // Convert to 24-hour format
            $searchDay = Carbon::parse($request->date)->format('l'); // Get day name (Monday, Tuesday, etc.)

            // Condition 2: Exclude services with existing bookings
            $bookedServiceIds = Booking::where('booking_date', $searchDate)
                ->where('booking_time', $searchTime)
                ->pluck('service_id')
                ->toArray();

            // Condition 3: Filter services based on slot availability
            $services = $services->filter(function ($service) use ($searchDate, $searchTime, $searchDay) {
                // Get service availability for the date
                $availability = $service->availabilities()
                    ->where('date', $searchDate)
                    ->where('day', $searchDay)
                    ->first();

                if (!$availability) return false;

                // Generate time slots based on service duration
                $slots = $this->generateTimeSlots($availability->start_time, $availability->end_time, $service->duration);

                // Check if search time falls in any slot
                foreach ($slots as $slot) {
                    if ($searchTime >= $slot['start'] && $searchTime < $slot['end']) {
                        // If today, check if slot time has passed
                        if (Carbon::parse($searchDate)->isToday()) {
                            $slotDateTime = Carbon::parse($searchDate . ' ' . $slot['start']);
                            if ($slotDateTime->isPast()) {
                                continue; // Skip this slot, check next one
                            }
                        }

                        // Check if this slot is not booked
                        $isBooked = Booking::where('service_id', $service->id)
                            ->where('booking_date', $searchDate)
                            ->where('booking_time', $slot['start'])
                            ->exists();

                        return !$isBooked;
                    }
                }

                return false;
            });
        }
        return view('website.search-results', compact('services'));
    }

    private function generateTimeSlots($startTime, $endTime, $duration)
    {
        $slots = [];
        $start = Carbon::parse($startTime);
        $end = Carbon::parse($endTime);

        while ($start->addMinutes($duration) <= $end) {
            $slotStart = $start->copy()->subMinutes($duration);
            $slots[] = [
                'start' => $slotStart->format('H:i:s'),
                'end' => $start->format('H:i:s')
            ];
        }

        return $slots;
    }

    public function professional_profile($id)
    {
        $user = User::where('ids', $id)
            ->with([
                'profile',
                'userCategories.category',
                'userCategories.subcategory',
                'socials',
                'galleries',
                'product_cerficates',
                'certificates',
                'allOpeningHours',
                'allHolidays',
                'introCards',
                'services',
                'staffs'
            ])
            ->firstOrFail();
        $userCategories = $user->userCategories->whereNotNull('category_id')
            ->unique('category_id')
            ->pluck('category')
            ->filter();

        $userSubcategories = $user->userCategories->whereNotNull('subcategory_id')
            ->pluck('subcategory')
            ->filter();
        $userSubcategoriesByCategory = $userSubcategories->groupBy('category_id');

        // Check if current user has favorited this professional
        $isFavorited = false;
        if (auth()->check()) {
            $isFavorited = auth()->user()->favoriteProfessionals()->where('professional_id', $user->id)->exists();
        }

        return view('dashboard.customer.professional_profile', compact('user', 'userCategories', 'userSubcategoriesByCategory', 'isFavorited'));
    }

    /**
     * Generate a unique short URL for a user's profile
     */
    public function generateShortUrl(Request $request)
    {
        try {
            $request->validate([
                'user_id' => 'required|exists:users,ids'
            ]);

            $userId = $request->user_id;
            $user = User::where("ids", $userId)->first();
            if ($user->short_url) {
                $fullShortUrl = url('/p/' . $user->short_url);
                return response()->json([
                    'success' => true,
                    'short_url' => $fullShortUrl,
                    'message' => 'Short URL retrieved successfully'
                ]);
            }
            $shortUrl = $this->generateUniqueShortUrl();
            $user->short_url = $shortUrl;
            $user->save();
            $fullShortUrl = url('/p/' . $shortUrl);
            return response()->json([
                'success' => true,
                'short_url' => $fullShortUrl,
                'message' => 'Short URL generated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate short URL. Please try again.'
            ], 500);
        }
    }

    private function generateUniqueShortUrl($length = 8)
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $maxAttempts = 100;
        $attempts = 0;

        do {
            $shortUrl = '';
            for ($i = 0; $i < $length; $i++) {
                $shortUrl .= $characters[rand(0, strlen($characters) - 1)];
            }

            $attempts++;

            // Check if this short URL already exists
            $exists = User::where('short_url', $shortUrl)->exists();
        } while ($exists && $attempts < $maxAttempts);

        if ($attempts >= $maxAttempts) {
            return $this->generateUniqueShortUrl($length + 1);
        }

        return $shortUrl;
    }
    public function redirectShortUrl($shortUrl)
    {
        $user = User::where('short_url', $shortUrl)->first();
        if (!$user) {
            abort(404, 'Short URL not found');
        }
        return redirect()->route('professional_profile', ['id' => $user->ids]);
    }
}

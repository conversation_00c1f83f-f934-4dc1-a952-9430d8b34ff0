{{-- availability modal --}}
<style>
.service-availibility-calendar .modal-dialog {
    max-width: 600px;
}
.service-availibility-calendar .day-row {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}
.service-availibility-calendar .day-row:last-child {
    border-bottom: none;
}
.service-availibility-calendar .form-check-input {
    margin-top: 0.25rem;
}
.service-availibility-calendar .form-control-sm {
    font-size: 0.875rem;
}
</style>

<div class="modal fade service-availibility-calendar" id="availabilityModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content p-3">
            <div class="modal-header">
                <h5 class="modal-title">Availability</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="availability-calendar">
                    <!-- Week Navigation -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="prevWeek">
                            <i class="fas fa-chevron-left"></i> Previous
                        </button>
                        <h6 class="mb-0" id="weekRange">Loading...</h6>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="nextWeek">
                            Next <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>

                    <!-- Days Container -->
                    <div id="weekDaysContainer">
                        <!-- Days will be populated by JavaScript -->
                    </div>

                    <!-- Recurring Options -->
                    <div class="mt-4">
                        <h6>Recurring Options</h6>
                        <div class="d-flex gap-3 flex-wrap">
                            <label class="form-check">
                                <input class="form-check-input" type="radio" name="recurring" value="1">
                                <span class="form-check-label">1 Week</span>
                            </label>
                            <label class="form-check">
                                <input class="form-check-input" type="radio" name="recurring" value="2">
                                <span class="form-check-label">2 Weeks</span>
                            </label>
                            <label class="form-check">
                                <input class="form-check-input" type="radio" name="recurring" value="4">
                                <span class="form-check-label">4 Weeks</span>
                            </label>
                            <label class="form-check">
                                <input class="form-check-input" type="radio" name="recurring" value="8">
                                <span class="form-check-label">8 Weeks</span>
                            </label>
                            <label class="form-check">
                                <input class="form-check-input" type="radio" name="recurring" value="custom">
                                <span class="form-check-label">Custom</span>
                            </label>
                        </div>

                        <!-- Custom Weeks Input -->
                        <div class="custom-weeks-input mt-3" style="display: none;">
                            <div class="d-flex gap-2 align-items-center">
                                <input type="number" class="form-control form-control-sm" id="customWeeks" 
                                       placeholder="Enter weeks" min="1" max="52" style="width: 120px;">
                                <button type="button" class="btn btn-primary btn-sm" id="customDone">Apply</button>
                            </div>
                        </div>
                    </div>

                    <!-- JSON Output (Hidden) -->
                    <textarea id="jsonOutput" style="display: none;"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveAvailability" data-bs-dismiss="modal">Save Availability</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script>
// Wrap everything in IIFE to prevent variable conflicts
(function() {
    const weekData = {};
    let currentWeekIndex = 0;
    const baseStartDate = moment().startOf('isoWeek'); // This will set the start date to the current week's Monday

    // 🎯 JSON DATA INITIALIZATION - Pass your array JSON data here
    const initializeDataFromJSON = () => {
        // 📋 YOUR JSON DATA - Replace this array with your actual API response
        const availabilityArray = @json(isset($service) ? $service->availabilities : []);

        // Convert array format to week-based format for the calendar
        availabilityArray.forEach(item => {
            const date = moment(item.date);
            const today = moment().startOf('day');

            // 🚫 SKIP PAST DATES - Don't process dates that are in the past
            if (date.isBefore(today, 'day')) {
                return; // Skip this iteration for past dates
            }

            const weekStart = date.clone().startOf('isoWeek'); // Get Monday of that week
            const weekKey = weekStart.format("YYYY-MM-DD");
            const dayName = item.day;

            // Convert time format from "HH:MM:SS" to "HH:MM"
            const startTime = item.start_time.substring(0, 5); // Remove seconds
            const endTime = item.end_time.substring(0, 5); // Remove seconds

            // Initialize week if it doesn't exist
            if (!weekData[weekKey]) {
                weekData[weekKey] = {
                    Monday: { enabled: false, start: "10:00", end: "19:00" },
                    Tuesday: { enabled: false, start: "10:00", end: "19:00" },
                    Wednesday: { enabled: false, start: "10:00", end: "19:00" },
                    Thursday: { enabled: false, start: "10:00", end: "19:00" },
                    Friday: { enabled: false, start: "10:00", end: "19:00" },
                    Saturday: { enabled: false, start: "10:00", end: "19:00" },
                    Sunday: { enabled: false, start: "10:00", end: "19:00" }
                };
            }

            // Set the specific day data (only for current and future dates)
            weekData[weekKey][dayName] = {
                enabled: true,
                start: startTime,
                end: endTime,
                id: item.id, // Store original ID for reference
                service_id: item.service_id // Store service_id for reference
            };
        });
    };

    // 🎯 EXTRACT SELECTED AVAILABILITY IN YOUR DESIRED FORMAT
    const getSelectedAvailability = () => {
        const selectedAvailability = [];

        // Loop through all weeks in weekData
        Object.keys(weekData).forEach(weekKey => {
            const weekStart = moment(weekKey); // Monday of the week
            const weekDays = weekData[weekKey];

            // Check each day of the week
            Object.keys(weekDays).forEach(dayName => {
                const dayData = weekDays[dayName];

                // Only include enabled days
                if (dayData.enabled) {
                    // Calculate the actual date for this day
                    const dayIndex = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"].indexOf(dayName);
                    const actualDate = weekStart.clone().add(dayIndex, 'days');

                    // Add to result array in your desired format
                    selectedAvailability.push({
                        "date": actualDate.format("YYYY-MM-DD"),
                        "day": dayName,
                        "start": dayData.start,
                        "end": dayData.end
                    });
                }
            });
        });

        // Sort by date for better organization
        selectedAvailability.sort((a, b) => moment(a.date).diff(moment(b.date)));
        return selectedAvailability;
    };

    // 📝 UPDATE TEXTAREA WITH JSON OUTPUT
    const updateJsonOutput = () => {
        const selectedData = getSelectedAvailability();
        const jsonString = JSON.stringify(selectedData, null, 2);
        $("#jsonOutput").val(jsonString);
    };

    const updateWeekUI = () => {
        const startOfWeek = baseStartDate.clone().add(currentWeekIndex * 7, "days");
        const weekDays = Array.from({ length: 7 }, (_, i) => startOfWeek.clone().add(i, "days"));
        const weekRange = `${weekDays[0].format("DD MMM YYYY")} - ${weekDays[6].format("DD MMM YYYY")}`;
        const weekKey = startOfWeek.format("YYYY-MM-DD");
        const week = weekData[weekKey] || {};

        $("#weekRange").text(weekRange);

        const dayNames = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
        const today = moment().startOf('day');

        const html = dayNames.map((day, index) => {
            const date = weekDays[index];
            const formattedDate = date.format("YYYY-MM-DD");
            const val = week[day] || { start: "10:00", end: "19:00", enabled: false };

            // 🚫 CHECK IF DATE IS IN THE PAST
            const isPastDate = date.isBefore(today, 'day');
            return `
            <div class="d-flex align-items-center mb-2 day-row" data-day="${day}" data-date="${formattedDate}">
                <input type="checkbox" class="form-check-input me-2 day-checkbox" data-day="${day}"
                       ${val.enabled ? "checked" : ""} ${isPastDate ? "disabled" : ""}>
                <label class="me-2 ${isPastDate ? 'text-muted' : ''}" style="width: 100px;">${day}</label>
                ${val.enabled && !isPastDate ? `
                    <input type="time" class="form-control form-control-sm me-2 start-time" value="${val.start}" data-day="${day}" style="width:120px;">
                    <span class="me-2">to</span>
                    <input type="time" class="form-control form-control-sm end-time" value="${val.end}" data-day="${day}" style="width:120px;">
                ` : `<span class="text-muted">${isPastDate ? "Past Date" : "Closed"}</span>`}
            </div>`;
        }).join("");

        $("#weekDaysContainer").html(html);
    };

    const saveCurrentWeekData = () => {
        const base = baseStartDate.clone().add(currentWeekIndex * 7, "days");
        const key = base.format("YYYY-MM-DD");
        weekData[key] = {};

        $(".day-row").each(function() {
            const day = $(this).data("day");
            const enabled = $(this).find(".day-checkbox").is(":checked");
            const start = $(this).find(".start-time").val() || "10:00";
            const end = $(this).find(".end-time").val() || "19:00";
            weekData[key][day] = { enabled, start, end };
        });
    };

    // Function to duplicate the weeks (with reset functionality)
    const duplicateWeeks = (weeks) => {
        const current = baseStartDate.clone().add(currentWeekIndex * 7, "days");
        const srcKey = current.format("YYYY-MM-DD");

        // 🔄 RESET: Clear all future week duplications first
        Object.keys(weekData).forEach(weekKey => {
            const weekDate = moment(weekKey);
            const currentWeekDate = moment(srcKey);

            // Remove any week that's after the current week and was previously duplicated
            if (weekDate.isAfter(currentWeekDate, 'week')) {
                // Check if this week has the same pattern as current week (indicating it was duplicated)
                const currentWeekData = weekData[srcKey];
                const weekToCheck = weekData[weekKey];

                // Only remove if it looks like a duplication (same enabled pattern)
                if (currentWeekData && weekToCheck) {
                    const currentEnabledDays = Object.keys(currentWeekData).filter(day => currentWeekData[day].enabled);
                    const checkEnabledDays = Object.keys(weekToCheck).filter(day => weekToCheck[day].enabled);

                    // If same number of enabled days, likely a duplication - remove it
                    if (currentEnabledDays.length === checkEnabledDays.length && currentEnabledDays.length > 0) {
                        delete weekData[weekKey];
                    }
                }
            }
        });

        // 📅 CREATE: Now create fresh duplications for the selected weeks
        for (let i = 1; i < weeks; i++) {
            const next = current.clone().add(i * 7, "days");
            const newKey = next.format("YYYY-MM-DD");
            // Create deep copy of current week's data
            weekData[newKey] = JSON.parse(JSON.stringify(weekData[srcKey]));
        }
    };

    // Validate time inputs
    const validateTimeInput = (input) => {
        const $input = $(input);
        const startTime = $input.val();
        const endTimeInput = $input.hasClass('start-time') ? 
            $input.closest('.day-row').find('.end-time') : 
            $input.closest('.day-row').find('.start-time');
        const endTime = endTimeInput.val();

        // Basic validation to ensure start time is before end time
        if (startTime && endTime && startTime >= endTime) {
            alert('Start time must be before end time');
            $input.focus();
        }
    };

    // Make functions globally available for form submission
    window.getSelectedAvailability = getSelectedAvailability;
    window.saveCurrentWeekData = saveCurrentWeekData;

    $(document).ready(function() {
        // Initialize data from JSON
        initializeDataFromJSON();
        updateWeekUI();
        updateJsonOutput();

        $(document).on("change", ".day-checkbox", function() {
            saveCurrentWeekData();
            updateWeekUI();
            updateJsonOutput(); // Update JSON when checkbox changes
        });

        // Validate time inputs when they change
        $(document).on("change", ".start-time, .end-time", function() {
            validateTimeInput(this);
            saveCurrentWeekData();
            updateJsonOutput(); // Update JSON when time changes
        });

        $("#prevWeek").click(function() {
            // Prevent going to past weeks
            if (currentWeekIndex > 0) {
                saveCurrentWeekData();
                currentWeekIndex--;
                updateWeekUI();
                updateJsonOutput(); // Update JSON when week changes
            }
        });

        $("#nextWeek").click(function() {
            saveCurrentWeekData();
            currentWeekIndex++;
            updateWeekUI();
            updateJsonOutput(); // Update JSON when week changes
        });

        $("#saveAvailability").click(function() {
            saveCurrentWeekData();
            updateJsonOutput(); // Final update of JSON
            const selectedData = getSelectedAvailability();
            alert("Availability Saved!");
        });

        // Recurring Radio Button Change
        $("input[name='recurring']").change(function() {
            const selected = $(this).val();
            saveCurrentWeekData();

            if (selected === "custom") {
                $(".custom-weeks-input").show();
            } else {
                $(".custom-weeks-input").hide();
            }

            if (selected === "custom") {
                return false;
            }

            const repeatWeeks = parseInt(selected);

            if (repeatWeeks > 0) {
                duplicateWeeks(repeatWeeks);
                updateJsonOutput(); // Update JSON after duplication
                alert(`Availability reset and duplicated for ${repeatWeeks} weeks total.`);
            }
        });

        // Handle Custom Weeks Input
        $("#customDone").click(function() {
            const customWeeks = parseInt($("#customWeeks").val());
            if (!isNaN(customWeeks) && customWeeks > 0) {
                saveCurrentWeekData();
                duplicateWeeks(customWeeks);
                updateJsonOutput(); // Update JSON after custom duplication
                alert(`Availability reset and duplicated for ${customWeeks} weeks total.`);
                $(".custom-weeks-input").hide();
                $("input[name='recurring']").prop('checked', false); // Uncheck radio buttons
            } else {
                alert("Please enter a valid number of weeks.");
            }
        });
    });
})();
</script>

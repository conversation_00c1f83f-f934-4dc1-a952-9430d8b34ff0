@extends('dashboard.layout.master')

@push('css')
    <style>
        /* Search loading indicator */
        .search_input.searching {
            opacity: 0.7;
            pointer-events: none;
        }

        /* Loading row styling */
        .loading-row td {
            text-align: center;
            padding: 2rem;
        }

        /* Wider Sweet<PERSON>lert for reject confirmation */
        .swal-wide {
            width: 600px !important;
        }

        .swal-wide .swal2-html-container {
            text-align: left !important;
        }
    </style>
@endpush

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Professionals</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                </div>
                <div class="col-lg-12">
                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active business-services" id="professionals-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-professionals" type="button" role="tab"
                                aria-controls="pills-professionals" aria-selected="true">
                                All Professionals
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link business-services" id="requests-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-requests" type="button" role="tab"
                                aria-controls="pills-requests" aria-selected="false">
                                New Requests
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-professionals" role="tabpanel"
                            aria-labelledby="professionals-tab" tabindex="0">
                            <div class="table-container">
                                <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                    <div class="search_box">
                                        <label for="customSearchInput">
                                            <i class="fas fa-search"></i>
                                        </label>
                                        <input class="search_input search" type="text" id="approvedSearchInput"
                                            placeholder="Search..." />
                                    </div>
                                    <!-- Select with dots -->
                                    <div class="dropdown search_box select-box">
                                        <button
                                            class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                            type="button" data-bs-toggle="dropdown" aria-expanded="false"
                                            id="approvedStatusDropdown">
                                            <span><span class="dot"></span>
                                                All</span>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item dropdown-status approved-status-filter"
                                                    href="#" data-status="all" data-label="All"
                                                    data-color="#4B5563"><span class="dot all"></span>
                                                    All</a></li>
                                            <li><a class="dropdown-item dropdown-status approved-status-filter"
                                                    href="#" data-status="active" data-label="Active"
                                                    data-color="#10B981"><span class="dot completed"></span>
                                                    Active</a></li>
                                            <li><a class="dropdown-item dropdown-status approved-status-filter"
                                                    href="#" data-status="inactive" data-label="Inactive"
                                                    data-color="#EF4444"><span class="dot cancelled-dot"></span>
                                                    Inactive</a></li>
                                        </ul>
                                    </div>
                                    <!-- Date Picker -->
                                    <label for="datePicker" class="date_picker">
                                        <div class="date-picker-container">
                                            <i class="bi bi-calendar-event calender-icon"></i>
                                            <input type="text" name="approvedDatePicker" id="approvedDatePicker"
                                                class="datePicker ms-3 w-200px" placeholder="Select date range" readonly>
                                            <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                        </div>
                                    </label>
                                    <!-- export btn -->
                                    <div class="search_box d-block ms-auto">
                                        <div class="dropdown">
                                            <button class="search_input fs-14 normal link-gray dropdown-toggle"
                                                type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                Export Data <i class="ms-1 bi bi-file-arrow-down"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item"
                                                        href="{{ route('professionals.export.approved') }}">
                                                        <i class="bi bi-file-earmark-excel me-2"></i>Export Approved
                                                        Professionals
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('professionals.export.all') }}">
                                                        <i class="bi bi-file-earmark-excel me-2"></i>Export All
                                                        Professionals
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <table id="responsiveTable" class="display w-100">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>Email Address</th>
                                            <th>Category</th>
                                            <th>Bookings</th>
                                            <th>Action</th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody id="approvedTableBody">
                                        @include('professional.partials.approved-table', [
                                            'approved_users' => $approved_users,
                                        ])
                                    </tbody>
                                </table>
                                <div class="d-flex justify-content-center" id="approvedPagination" style="{{ $approved_users->hasPages() ? '' : 'display: none;' }}">
                                    @if($approved_users->hasPages())
                                        {{ $approved_users->links('pagination::bootstrap-4') }}
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-requests" role="tabpanel" aria-labelledby="requests-tab"
                            tabindex="1">
                            <div class="table-container">
                                <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                    <div class="search_box">
                                        <label for="customSearchInput">
                                            <i class="fas fa-search"></i>
                                        </label>
                                        <input class="search_input search" type="text" id="pendingSearchInput"
                                            placeholder="Search..." />
                                    </div>

                                    <!-- Date Picker -->
                                    <label for="datePicker" class="date_picker">
                                        <div class="date-picker-container">
                                            <i class="bi bi-calendar-event calender-icon"></i>
                                            <input type="text" name="pendingDatePicker" id="pendingDatePicker"
                                                class="datePicker ms-3 w-200px" placeholder="Select date range" readonly>
                                            <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                        </div>
                                    </label>
                                    <!-- export btn -->
                                    <div class="search_box d-block ms-auto">
                                        <div class="dropdown">
                                            <button class="search_input fs-14 normal link-gray dropdown-toggle"
                                                type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                Export Data <i class="ms-1 bi bi-file-arrow-down"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item"
                                                        href="{{ route('professionals.export.unapproved') }}">
                                                        <i class="bi bi-file-earmark-excel me-2"></i>Export Pending
                                                        Professionals
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item"
                                                        href="{{ route('professionals.export.all') }}">
                                                        <i class="bi bi-file-earmark-excel me-2"></i>Export All
                                                        Professionals
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                </div>
                                <table id="responsiveTable" class="display w-100">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>Email Address</th>
                                            <th>Category</th>
                                            <th>Action</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody id="pendingTableBody">
                                        @include('professional.partials.pending-table', [
                                            'unapproved_users' => $unapproved_users,
                                        ])
                                    </tbody>
                                </table>
                                <div class="d-flex justify-content-center" id="pendingPagination" style="{{ $unapproved_users->hasPages() ? '' : 'display: none;' }}">
                                    @if($unapproved_users->hasPages())
                                        {{ $unapproved_users->links('pagination::bootstrap-4') }}
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script>
        // Function to parse different date filter formats
        function parseDateFilter(dateValue) {
            if (!dateValue || dateValue.trim() === '') return null;

            let today = new Date();
            let result = {};

            // Check for predefined options
            if (dateValue.toLowerCase().includes('today')) {
                result.start_date = formatDate(today);
                result.end_date = formatDate(today);
                result.type = 'single';
            } else if (dateValue.toLowerCase().includes('yesterday')) {
                let yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                result.start_date = formatDate(yesterday);
                result.end_date = formatDate(yesterday);
                result.type = 'single';
            } else if (dateValue.toLowerCase().includes('last week')) {
                let lastWeekStart = new Date(today);
                lastWeekStart.setDate(today.getDate() - 7);
                result.start_date = formatDate(lastWeekStart);
                result.end_date = formatDate(today);
                result.type = 'range';
            } else if (dateValue.toLowerCase().includes('last month')) {
                let lastMonthStart = new Date(today);
                lastMonthStart.setMonth(today.getMonth() - 1);
                result.start_date = formatDate(lastMonthStart);
                result.end_date = formatDate(today);
                result.type = 'range';
            } else if (dateValue.includes(' - ')) {
                // Custom date range format: "MMM D, YYYY - MMM D, YYYY"
                let dates = dateValue.split(' - ');
                if (dates.length === 2) {
                    try {
                        let startDate = new Date(dates[0].trim());
                        let endDate = new Date(dates[1].trim());
                        result.start_date = formatDate(startDate);
                        result.end_date = formatDate(endDate);
                        result.type = 'range';
                    } catch (e) {
                        console.error('Error parsing date range:', e);
                        return null;
                    }
                }
            } else {
                // Single date
                try {
                    let singleDate = new Date(dateValue);
                    result.start_date = formatDate(singleDate);
                    result.end_date = formatDate(singleDate);
                    result.type = 'single';
                } catch (e) {
                    console.error('Error parsing single date:', e);
                    return null;
                }
            }

            return result;
        }

        // Helper function to format date as YYYY-MM-DD
        function formatDate(date) {
            return date.getFullYear() + '-' +
                   String(date.getMonth() + 1).padStart(2, '0') + '-' +
                   String(date.getDate()).padStart(2, '0');
        }

        $(document).ready(function() {
            let currentTab = 'approved';
            let approvedStatus = 'all';
            let searchTimeout;
            let approvedDatePickerUsed = false; // Track if approved date picker was actually used
            let pendingDatePickerUsed = false; // Track if pending date picker was actually used

            // Clear any existing values and destroy existing daterangepicker if it exists
            $('#approvedDatePicker, #pendingDatePicker').val('');
            $('#approvedDatePicker, #pendingDatePicker').each(function() {
                if ($(this).data('daterangepicker')) {
                    $(this).data('daterangepicker').remove();
                }
            });

            // Initialize professional date pickers with no default values
            $('#approvedDatePicker, #pendingDatePicker').daterangepicker({
                autoUpdateInput: false,
                opens: 'center',
                locale: {
                    format: 'MMM D, YYYY',
                    cancelLabel: 'Clear'
                },
                ranges: {
                    'Today': [moment(), moment()],
                    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    'Last Week': [moment().subtract(6, 'days'), moment()],
                    'Last Month': [moment().subtract(29, 'days'), moment()],
                    'This Month': [moment().startOf('month'), moment().endOf('month')]
                }
            });

            // Ensure the inputs are empty after initialization
            $('#approvedDatePicker, #pendingDatePicker').val('');

            // Clear any existing values and destroy existing daterangepicker if it exists
            $('#approvedDatePicker, #pendingDatePicker').val('');
            $('#approvedDatePicker, #pendingDatePicker').each(function() {
                if ($(this).data('daterangepicker')) {
                    $(this).data('daterangepicker').remove();
                }
            });

            // Initialize professional date pickers with no default values
            $('#approvedDatePicker, #pendingDatePicker').daterangepicker({
                autoUpdateInput: false,
                opens: 'center',
                locale: {
                    format: 'MMM D, YYYY',
                    cancelLabel: 'Clear'
                },
                ranges: {
                    'Today': [moment(), moment()],
                    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    'Last Week': [moment().subtract(6, 'days'), moment()],
                    'Last Month': [moment().subtract(29, 'days'), moment()],
                    'This Month': [moment().startOf('month'), moment().endOf('month')]
                }
            });

            // Ensure the inputs are empty after initialization
            $('#approvedDatePicker, #pendingDatePicker').val('');

            // Function to filter professionals
            function filterProfessionals(tab = null) {
                if (tab) currentTab = tab;

                let search = '';
                let status = '';
                let date = '';

                if (currentTab === 'approved') {
                    search = $('#approvedSearchInput').val().trim();
                    status = approvedStatus;
                    date = $('#approvedDatePicker').val().trim();
                } else {
                    search = $('#pendingSearchInput').val().trim();
                    status = 'pending'; // Always pending for new requests tab
                    date = $('#pendingDatePicker').val().trim();
                }

                // Only parse date if date picker was actually used
                let dateFilter = null;
                if (currentTab === 'approved' && approvedDatePickerUsed && date) {
                    dateFilter = parseDateFilter(date);
                } else if (currentTab === 'pending' && pendingDatePickerUsed && date) {
                    dateFilter = parseDateFilter(date);
                }

                // Prepare data object - only include non-empty values
                let data = {
                    tab: currentTab
                };

                if (search) {
                    data.search = search;
                }

                if (status && status !== 'all') {
                    data.status = status;
                }

                // Only include date data if date picker was actually used
                if ((currentTab === 'approved' && approvedDatePickerUsed && date) ||
                    (currentTab === 'pending' && pendingDatePickerUsed && date)) {
                    data.date = date;
                    if (dateFilter) {
                        data.date_filter = dateFilter;
                    }
                }

                console.log('Professional filter data:', data);

                // Show loading state
                showLoadingState();

                $.ajax({
                    url: "{{ route('professionals.filter') }}",
                    type: "GET",
                    data: data,
                    beforeSend: function() {
                        // Add searching class to search inputs
                        $('#approvedSearchInput, #pendingSearchInput').addClass('searching');
                    },
                    success: function(response) {
                        console.log('Professional filter response:', response);
                        if (response.success) {
                            if (currentTab === 'approved') {
                                $('#approvedTableBody').html(response.html);

                                // Handle pagination for approved tab
                                if (response.has_pages && response.count > 0 && response.pagination && response.pagination.trim() !== '') {
                                    $('#approvedPagination').html(response.pagination).show();
                                } else {
                                    $('#approvedPagination').html('').hide().css('display', 'none');
                                }
                            } else {
                                $('#pendingTableBody').html(response.html);

                                // Handle pagination for pending tab
                                if (response.has_pages && response.count > 0 && response.pagination && response.pagination.trim() !== '') {
                                    $('#pendingPagination').html(response.pagination).show();
                                } else {
                                    $('#pendingPagination').html('').hide().css('display', 'none');
                                }
                            }

                            console.log('Professional table updated with', response.count, 'users');

                            // Show success toast with result count
                            if (data.search || data.date || data.status !== 'all') {
                                if (response.count === 0) {
                                    showToast('No results found for your search criteria', 'warning');
                                } else {
                                    showToast(`Found ${response.count} result(s)`, 'success');
                                }
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Professional filter error:', error);
                        console.error('Response:', xhr.responseText);
                        showToast('Search failed. Please check your connection.', 'error');
                    },
                    complete: function() {
                        // Remove loading states
                        $('#approvedSearchInput, #pendingSearchInput').removeClass('searching');
                        hideLoadingState();
                    }
                });
            }

            // Search on keyup with debounce
            $('#approvedSearchInput, #pendingSearchInput').on('keyup', function() {
                console.log('Professional search input changed:', $(this).val());

                // Clear previous timeout
                clearTimeout(searchTimeout);

                // Set new timeout for debounce (300ms delay)
                searchTimeout = setTimeout(function() {
                    filterProfessionals();
                }, 300);
            });

            // Status filter for approved tab
            $('.approved-status-filter').on('click', function(e) {
                e.preventDefault();
                let label = $(this).data('label');
                let color = $(this).data('color');
                let status = $(this).data('status');

                approvedStatus = status;
                $('#approvedStatusDropdown .dropdown-toggle span').html(
                    '<span class="dot" style="background-color: ' + color + '"></span> ' + label);
                filterProfessionals();
            });

            // Handle approved date range picker apply event
            $('#approvedDatePicker').on('apply.daterangepicker', function(ev, picker) {
                approvedDatePickerUsed = true; // Mark that approved date picker was used
                if (picker.startDate.isSame(picker.endDate, 'day')) {
                    $(this).val(picker.startDate.format('MMM D, YYYY'));
                } else {
                    $(this).val(picker.startDate.format('MMM D, YYYY') + ' - ' + picker.endDate.format('MMM D, YYYY'));
                }
                console.log('Approved date range applied:', $(this).val());
                filterProfessionals();
            });

            // Handle pending date range picker apply event
            $('#pendingDatePicker').on('apply.daterangepicker', function(ev, picker) {
                pendingDatePickerUsed = true; // Mark that pending date picker was used
                if (picker.startDate.isSame(picker.endDate, 'day')) {
                    $(this).val(picker.startDate.format('MMM D, YYYY'));
                } else {
                    $(this).val(picker.startDate.format('MMM D, YYYY') + ' - ' + picker.endDate.format('MMM D, YYYY'));
                }
                console.log('Pending date range applied:', $(this).val());
                filterProfessionals();
            });

            // Handle date range picker cancel/clear events
            $('#approvedDatePicker').on('cancel.daterangepicker', function(ev, picker) {
                approvedDatePickerUsed = false; // Reset approved date picker usage flag
                $(this).val('');
                console.log('Approved date picker cleared');
                filterProfessionals();
            });

            $('#pendingDatePicker').on('cancel.daterangepicker', function(ev, picker) {
                pendingDatePickerUsed = false; // Reset pending date picker usage flag
                $(this).val('');
                console.log('Pending date picker cleared');
                filterProfessionals();
            });

            // Handle manual input changes (only if date picker was used)
            $('#approvedDatePicker').on('change', function() {
                if (approvedDatePickerUsed) {
                    console.log('Approved date picker manually changed:', $(this).val());
                    filterProfessionals();
                }
            });

            $('#pendingDatePicker').on('change', function() {
                if (pendingDatePickerUsed) {
                    console.log('Pending date picker manually changed:', $(this).val());
                    filterProfessionals();
                }
            });

            // Handle down arrow click to open date pickers
            $('.date-picker-container .down-arrow').on('click', function() {
                var input = $(this).siblings('input.datePicker');
                if (input.data('daterangepicker')) {
                    input.data('daterangepicker').show();
                }
            });

            // Tab change
            $('#professionals-tab').on('click', function() {
                currentTab = 'approved';
                filterProfessionals('approved');
            });

            $('#requests-tab').on('click', function() {
                currentTab = 'pending';
                filterProfessionals('pending');
            });

            // Helper functions for loading state and toast notifications
            function showLoadingState() {
                const loadingHtml = `
                    <tr class="loading-row">
                        <td colspan="8" class="text-center py-4">
                            <div class="d-flex justify-content-center align-items-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span>Searching...</span>
                            </div>
                        </td>
                    </tr>
                `;

                if (currentTab === 'approved') {
                    $('#approvedTableBody').html(loadingHtml);
                } else {
                    $('#pendingTableBody').html(loadingHtml);
                }
            }

            function hideLoadingState() {
                $('.loading-row').remove();
            }

            function showToast(message, type) {
                const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
                const toast = `
                    <div class="toast align-items-center text-white ${toastClass} border-0 position-fixed top-0 end-0 m-3" role="alert" style="z-index: 9999;">
                        <div class="d-flex">
                            <div class="toast-body">${message}</div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    </div>
                `;

                $('body').append(toast);
                const toastElement = $('.toast').last();
                const bsToast = new bootstrap.Toast(toastElement[0], {
                    delay: 3000
                });
                bsToast.show();

                // Remove toast element after it's hidden
                toastElement.on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            }

            // Handle reject professional button click
            $(document).on('click', '.reject-professional', function() {
                const userId = $(this).data('user-id');
                const userName = $(this).data('user-name');
                const userEmail = $(this).data('user-email');

                Swal.fire({
                    title: 'Reject Professional?',
                    html: `
                        <div class="text-start">
                            <p><strong>Professional:</strong> ${userName}</p>
                            <p><strong>Email:</strong> ${userEmail}</p>
                            <br>
                            <p class="text-danger"><strong>Warning:</strong> This action will:</p>
                            <ul class="text-start text-danger">
                                <li>Send a rejection email to the professional</li>
                                <li>Permanently delete all their data</li>
                                <li>Remove all related records from the database</li>
                            </ul>
                            <p class="text-danger"><strong>This action cannot be undone!</strong></p>
                        </div>
                    `,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, Reject & Delete',
                    cancelButtonText: 'Cancel',
                    reverseButtons: true,
                    customClass: {
                        popup: 'swal-wide'
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Show loading state
                        Swal.fire({
                            title: 'Processing...',
                            text: 'Rejecting professional and deleting data...',
                            allowOutsideClick: false,
                            allowEscapeKey: false,
                            showConfirmButton: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        // Redirect to reject route
                        window.location.href = `{{ route('professional.reject', '') }}/${userId}`;
                    }
                });
            });
        });
    </script>
@endpush

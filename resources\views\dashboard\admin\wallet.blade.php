@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Wallet</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                </div>
                <div class="row row-gap-5 mb-10 card-wrapper">
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-blue">
                                        @include('svg.customer')
                                    </div>
                                </div>
                                <div class="card-body w-50">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Paid by Customer
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="{{ $walletStats['paid_by_customers'] ?? 0 }}" data-kt-countup-prefix="$">
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-green">
                                        @include('svg.professional')
                                    </div>
                                </div>
                                <div class="card-body w-50 ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Paid by Professionals
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="{{ $walletStats['paid_by_professionals'] ?? 0 }}" data-kt-countup-prefix="$">
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-purple">
                                        @include('svg.dollar')
                                    </div>
                                </div>
                                <div class="card-body w-50 ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Total Revenue
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="{{ $walletStats['total_revenue'] ?? 0 }}" data-kt-countup-prefix="$">
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-orange">
                                        @include('svg.calender')
                                    </div>
                                </div>
                                <div class="card-body w-50 ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Number of Bookings
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="{{ $walletStats['number_of_bookings'] ?? 0 }}">
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="col-lg-12">
                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active business-services" id="cusTransaction-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-cusTransaction" type="button" role="tab"
                                aria-controls="pills-cusTransaction" aria-selected="true">
                                Customers Transaction
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link business-services" id="profTransaction-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-profTransaction" type="button" role="tab"
                                aria-controls="pills-profTransaction" aria-selected="false">
                                Professionals Transaction
                            </button>
                        </li>
                    </ul>


                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-cusTransaction" role="tabpanel"
                            aria-labelledby="cusTransaction-tab" tabindex="0">
                            <div class="table-container">
                                <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                    <div class="search_box">
                                        <label for="customSearchInput">
                                            <i class="fas fa-search"></i>
                                        </label>
                                        <input class="search_input search" type="text" id="customSearchInput"
                                            placeholder="Search..." />
                                    </div>
                                    <!-- Select with dots -->
                                    <div class="dropdown search_box select-box">
                                        <button
                                            class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                            type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <span><span class="dot"></span>
                                                All</span>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                                    data-color="#4B5563"><span class="dot all"></span>
                                                    All</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Ongoing"
                                                    data-color="#F59E0B"><span class="dot ongoing"></span>
                                                    Ongoing</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Upcoming"
                                                    data-color="#3B82F6"><span class="dot upcoming"></span>
                                                    Upcoming</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Complete"
                                                    data-color="#10B981"><span class="dot completed"></span>
                                                    Complete</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Canceled"
                                                    data-color="#EF4444"><span class="dot cancelled-dot"></span>
                                                    Canceled</a></li>
                                        </ul>
                                    </div>
                                    <!-- category -->
                                    {{-- <div class="search_box select-box">
                                        <select class="search_input" id="customerCategoryFilter">
                                            <option value="Category">Category</option>
                                            <option value="Category">All</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->name }}">{{ $category->name }}</option>
                                            @endforeach
                                        </select>
                                    </div> --}}
                                    <!-- Date Picker -->
                                    <label for="datePicker" class="date_picker">
                                        <div class="date-picker-container">
                                            <i class="bi bi-calendar-event calender-icon"></i>
                                            <input type="text" name="datePicker" class="datePicker ms-3 w-200px">
                                            <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                        </div>
                                    </label>
                                    <!-- export btn -->
                                    <div class="search_box d-block ms-auto">
                                        <button class="search_input fs-14 normal link-gray" id="exportCustomersBtn">
                                            Export Data <i class="ms-1 bi bi-file-arrow-down file-icon"></i>
                                        </button>
                                    </div>
                                </div>
                                <table id="responsiveTable" class=" display w-100">
                                    <thead>
                                        <tr>
                                            <th>customer name</th>
                                            <th>Email Address</th>
                                            <th>Bookings</th>
                                            <th>Action</th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody id="customersTableBody">
                                        @include('dashboard.admin.partials.wallet-customers-table', ['customers' => $customers])
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-profTransaction" role="tabpanel"
                            aria-labelledby="profTransaction-tab" tabindex="1">
                            <div class="table-container">
                                <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                    <div class="search_box">
                                        <label for="customSearchInput">
                                            <i class="fas fa-search"></i>
                                        </label>
                                        <input class="search_input search" type="text" id="customSearchInput"
                                            placeholder="Search..." />
                                    </div>
                                    <!-- Select with dots -->
                                    <div class="dropdown search_box select-box">
                                        <button
                                            class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                            type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <span><span class="dot"></span>
                                                All</span>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                                    data-color="#4B5563"><span class="dot all"></span>
                                                    All</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Ongoing"
                                                    data-color="#F59E0B"><span class="dot ongoing"></span>
                                                    Ongoing</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Upcoming"
                                                    data-color="#3B82F6"><span class="dot upcoming"></span>
                                                    Upcoming</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Complete"
                                                    data-color="#10B981"><span class="dot completed"></span>
                                                    Complete</a></li>
                                            <li><a class="dropdown-item dropdown-status" href="#" data-label="Canceled"
                                                    data-color="#EF4444"><span class="dot cancelled-dot"></span>
                                                    Canceled</a></li>
                                        </ul>
                                    </div>
                                    <!-- Date Picker -->
                                    <label for="datePicker2" class="date_picker">
                                        <div class="date-picker-container">
                                            <i class="bi bi-calendar-event calender-icon"></i>
                                            <input type="text" name="datePicker2" class="datePicker ms-3 w-200px">
                                            <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                        </div>
                                    </label>
                                    <!-- export btn -->
                                    <div class="search_box d-block ms-auto">
                                        <button class="search_input fs-14 normal link-gray" id="exportProfessionalsBtn">
                                            Export Data <i class="ms-1 bi bi-file-arrow-down file-icon"></i>
                                        </button>
                                    </div>

                                </div>
                                <table id="responsiveTable" class="responsiveTable displayw-100">
                                    <thead>
                                        <tr>
                                            <th>customer name</th>
                                            <th>Email Address</th>
                                            <th>Bookings</th>
                                            <th>Action</th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody id="professionalsTableBody">
                                        @include('dashboard.admin.partials.wallet-professionals-table', ['professionals' => $professionals])
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
<script>
    $(document).ready(function() {
        let searchTimeout;
        let currentCustomerStatus = 'all';
        let currentProfessionalStatus = 'all';
        let currentCustomerCategory = 'Category';

        // Customer tab search and filtering
        $('#pills-cusTransaction').on('shown.bs.tab', function() {
            // Customer search input with debouncing
            $('#customSearchInput').off('keyup').on('keyup', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    performCustomerSearch();
                }, 300);
            });

            // Customer status dropdown handling
            $('.dropdown-status').off('click').on('click', function(e) {
                e.preventDefault();
                const label = $(this).data('label');
                const color = $(this).data('color');

                // Update button text and color
                const button = $(this).closest('.dropdown').find('.status-dropdown-button span');
                button.html(`<span class="dot" style="background-color: ${color}"></span> ${label}`);

                currentCustomerStatus = label.toLowerCase();
                performCustomerSearch();
            });

            // Customer category dropdown handling
            $('#customerCategoryFilter').off('change').on('change', function() {
                currentCustomerCategory = $(this).val();
                performCustomerSearch();
            });
        });

        // Professional tab search and filtering
        $('#pills-profTransaction').on('shown.bs.tab', function() {
            // Professional search input with debouncing
            $('#customSearchInput').off('keyup').on('keyup', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    performProfessionalSearch();
                }, 300);
            });

            // Professional status dropdown handling
            $('.dropdown-status').off('click').on('click', function(e) {
                e.preventDefault();
                const label = $(this).data('label');
                const color = $(this).data('color');

                // Update button text and color
                const button = $(this).closest('.dropdown').find('.status-dropdown-button span');
                button.html(`<span class="dot" style="background-color: ${color}"></span> ${label}`);

                currentProfessionalStatus = label.toLowerCase();
                performProfessionalSearch();
            });
        });

        function performCustomerSearch() {
            const searchQuery = $('#customSearchInput').val();

            // Show loading state
            $('#customersTableBody').html('<tr><td colspan="7" class="text-center">Loading...</td></tr>');

            $.ajax({
                url: '{{ route('wallet.filter.customers') }}',
                type: 'GET',
                data: {
                    search: searchQuery,
                    status: currentCustomerStatus,
                    category: currentCustomerCategory
                },
                success: function(response) {
                    if (response.success) {
                        $('#customersTableBody').html(response.html);
                    } else {
                        $('#customersTableBody').html(
                            '<tr><td colspan="7" class="text-center">Error loading customers</td></tr>'
                        );
                    }
                },
                error: function() {
                    $('#customersTableBody').html(
                        '<tr><td colspan="7" class="text-center">Error loading customers</td></tr>'
                    );
                }
            });
        }

        function performProfessionalSearch() {
            const searchQuery = $('#customSearchInput').val();

            // Show loading state
            $('#professionalsTableBody').html('<tr><td colspan="7" class="text-center">Loading...</td></tr>');

            $.ajax({
                url: '{{ route('wallet.filter.professionals') }}',
                type: 'GET',
                data: {
                    search: searchQuery,
                    status: currentProfessionalStatus
                },
                success: function(response) {
                    if (response.success) {
                        $('#professionalsTableBody').html(response.html);
                    } else {
                        $('#professionalsTableBody').html(
                            '<tr><td colspan="7" class="text-center">Error loading professionals</td></tr>'
                        );
                    }
                },
                error: function() {
                    $('#professionalsTableBody').html(
                        '<tr><td colspan="7" class="text-center">Error loading professionals</td></tr>'
                    );
                }
            });
        }

        // Export customers
        $(document).on('click', '#exportCustomersBtn', function(e) {
            e.preventDefault();

            // Show loading state
            const originalText = $(this).html();
            $(this).html('<span class="spinner-border spinner-border-sm me-2" role="status"></span>Exporting...');

            // Trigger download
            window.location.href = '{{ route("wallet.export.customers") }}';

            // Reset button after a delay
            setTimeout(function() {
                $('#exportCustomersBtn').html(originalText);
            }, 2000);
        });

        // Export professionals
        $(document).on('click', '#exportProfessionalsBtn', function(e) {
            e.preventDefault();

            // Show loading state
            const originalText = $(this).html();
            $(this).html('<span class="spinner-border spinner-border-sm me-2" role="status"></span>Exporting...');

            // Trigger download
            window.location.href = '{{ route("wallet.export.professionals") }}';

            // Reset button after a delay
            setTimeout(function() {
                $('#exportProfessionalsBtn').html(originalText);
            }, 2000);
        });

        // Initialize customer search on page load
        performCustomerSearch();
    });
</script>
@endpush
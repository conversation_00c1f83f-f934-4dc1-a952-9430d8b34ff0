@forelse ($customers as $customer)
    <tr>
        <td data-label="CUSTOMER NAME">{{ $customer->name ?? 'N/A' }}</td>
        <td data-label="EMAIL ADDRESS">{{ $customer->email ?? 'N/A' }}</td>
        <td data-label="CATEGORY">
            @php
                $categories = $customer->bookings->pluck('service.category.name')->filter()->unique();
            @endphp
            {{ $categories->count() > 0 ? $categories->implode(', ') : 'N/A' }}
        </td>
        <td data-label="BOOKINGS">{{ $customer->bookings->count() }}</td>
        <td data-label="STATUS" class="request-status status paid-status">
            @php
                $completedBookings = $customer->bookings->where('status', 1)->count();
                $totalBookings = $customer->bookings->count();
            @endphp
            {{ $completedBookings }}/{{ $totalBookings }} Completed
        </td>
        <td data-label="DATE & TIME">
            {{ $customer->created_at ? \Carbon\Carbon::parse($customer->created_at)->format('d M, Y') : 'N/A' }}
            <span class="wallet-time status unpaid-status ps-4">
                {{ $customer->created_at ? \Carbon\Carbon::parse($customer->created_at)->format('g:i A') : '' }}
            </span>
        </td>
        <td data-label="">
            <div class="dropdown">
                <button class="drop-btn" type="button" id="dropdownMenuButton"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <li>
                        <a class="dropdown-item view fs-14 regular" href="{{ route('customer.show', $customer->id) }}">
                            <i class="bi bi-eye view-icon"></i>
                            View Details
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item fs-14 regular" href="{{ route('customer.change_status', $customer->id) }}">
                            <i class="bi bi-toggle-on"></i>
                            Change Status
                        </a>
                    </li>
                </ul>
            </div>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="7" class="text-center">No customers found</td>
    </tr>
@endforelse

<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class FriendInvitationMail extends Mailable
{
    use Queueable, SerializesModels;

    public $senderName;
    public $registrationUrl;

    /**
     * Create a new message instance.
     */
    public function __construct($senderName, $registrationUrl)
    {
        $this->senderName = $senderName;
        $this->registrationUrl = $registrationUrl;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Friend Invitation - Join Our Platform',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'mails.friend_invitation',
            with: [
                'senderName' => $this->senderName,
                'registrationUrl' => $this->registrationUrl,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}

@extends('layouts.app')

@push('css')
    <style>
        .user-type-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-type-card:hover .gray-card {
            border: 2px solid #007bff;
            background-color: #f8f9fa;
        }

        .user-type-card.active .gray-card {
            border: 2px solid #007bff;
            background-color: #e3f2fd;
        }

        .card-radio {  display: none; }
    </style>
@endpush

@section('content')
    <div class="container-fluid first-stepper-form ">
        <div class="row">
            <div class="col-md-6 p-20">
                <form id="register-stepper-form" class="register-form form w-100 mb-10" novalidate="novalidate"
                    method="POST" method="POST" action="{{ route('register') }}">
                    <div>
                        <i name="previous" value="ll" class="fas fa-chevron-left previous action-button-previous"></i>
                    </div>

                    @csrf
                    <fieldset class="step-1">
                        <h3 class="text-center mb-10">Sign up/Login</h3>

                        <label class="w-100 user-type-card" data-user-type="customer">
                            <input type="radio" name="user_type" value="customer" class="card-radio"
                                {{ request('invited_by') ? 'checked' : '' }}>
                            <div class="gray-card d-flex justify-content-between align-items-center mb-5">
                                <div>
                                    <h5 class="fs-16 fw-600">For customers</h5>
                                    <p class="fs-14 gray-text">Book salons and spas near you</p>
                                </div>
                                <img src="{{ asset('website') }}/assets/images/right-arrow.svg" alt="icon">
                            </div>
                        </label>

                        @if(!request('invited_by'))
                        <label class="w-100 user-type-card" data-user-type="professional">
                            <input type="radio" name="user_type" value="professional" class="card-radio">
                            <div class="gray-card d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="fs-16 fw-600">For professionals</h5>
                                    <p class="fs-14 gray-text">Manage your salon or spa business</p>
                                </div>
                                <img src="{{ asset('website') }}/assets/images/right-arrow.svg" alt="icon">
                            </div>
                        </label>
                        @endif


                        <!-- <div class="gray-card d-flex justify-content-between align-items-center mb-5 next action-button"
                                                                                                    value="Next">
                                                                                                    <div>
                                                                                                        <h5 class="fs-16 fw-600">For customers</h5>
                                                                                                        <p class="fs-14 gray-text">Book salons and spas near you</p>
                                                                                                    </div>
                                                                                                    <img src="{{ asset('website') }}/assets/images/right-arrow.svg" class="next action-button"
                                                                                                        value="Next" alt="icon">
                                                                                                </div> -->

                        <!-- <div class="gray-card d-flex justify-content-between align-items-center next action-button"
                                                                                                    value="Next">
                                                                                                    <div>
                                                                                                        <h5 class="fs-16 fw-600">For professionals</h5>
                                                                                                        <p class="fs-14 gray-text">Book salons and spas near you</p>
                                                                                                    </div>
                                                                                                    <img src="{{ asset('website') }}/assets/images/right-arrow.svg" class="next action-button"
                                                                                                        alt="icon">
                                                                                                </div> -->
                    </fieldset>

                    <fieldset class="step-2">
                        <div class="mb-10">
                            <h3 class="text-center step-2-title">Stylenest for professionals</h3>
                            <p class="fs-14 text-center step-2-description">Create an account or log in to manage your
                                business.</p>
                        </div>

                        <div class="mb-8">
                            <input id="email" type="email" placeholder="Enter your email address"
                                class="form-control mb-6 bg-transparent @error('email') is-invalid @enderror" name="email"
                                value="{{ old('email', request('email')) }}" required autocomplete="email"
                                {{ request('invited_by') ? 'readonly' : '' }}>

                            @error('name')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror

                            <button type="button" class="action-button blue-btn fs-6 fw-600 send-otp-btn"> Continue
                            </button>
                        </div>

                        <div class="separator separator-content my-14">
                            <span class="w-125px text-gray-500 fw-semibold fs-7">OR</span>
                        </div>

                        <div class="mb-5">
                            <a href="{{ route('google.login.with.type', ['user_type' => 'placeholder']) }}"
                                class="btn btn-flex mb-5 btn-outline btn-text-gray-700 bg-state-light flex-center text-nowrap w-100 google-login-btn">
                                <img class="pe-4" src="{{ asset('website') }}/assets/images/Google_Logo.svg" alt="icon">
                                Sign in with Google</a>

                            <a href="{{ route('apple.login.with.type', ['user_type' => 'placeholder']) }}"
                                class="btn btn-flex btn-outline btn-text-gray-700 bg-state-light flex-center text-nowrap w-100 apple-login-btn">
                                <img class="pe-4" src="{{ asset('website') }}/assets/images/Apple_Logo.svg" alt="icon"> Sign
                                in with Apple</a>
                        </div>

                    </fieldset>

                    <fieldset class="step-3">
                        <div class="mb-10">
                            <h3 class="text-center">Verify your identity</h3>
                            <p class="fs-14 text-center">To protect your account, we'll send a text message with a 6-
                                digit code to <span class="dynamic-email">your email</span>.</p>
                        </div>

                        <div class="mb-8 position-relative">
                            <div class="d-flex justify-content-between">
                                <label for="otp" class="add-btn fs-14 fw-500 mb-3">Enter OTP</label>

                                <div class="timer-container">
                                    <span class="fs-14 text-muted">OTP expires in: </span>
                                    <span id="otp-timer" class="fs-14 fw-600 text-danger">02:00</span>
                                </div>
                            </div>

                            <input id="otp" type="number" placeholder="Enter OTP"
                                class="form-control mb-6 bg-transparent pe-5" name="otp">

                            <span id="toggle-password-otp"
                                class="btn-sm btn-icon position-absolute top-50 translate-middle-y end-0 pt-9 me-3 cursor-pointer">
                                <i class="fa-solid fa-eye"></i>
                                <i class="fa-solid fa-eye-slash d-none"></i>
                            </span>

                            <div class="d-flex mt-3">

                                <button type="button" class="verify-otp-btn action-button blue-btn fs-6 fw-600">Verify
                                    OTP</button>
                            </div>
                        </div>
                    </fieldset>

                    <fieldset class="step-4">
                        <div class="mb-10">
                            <h3 class="text-center">Set Your Password</h3>
                            <p class="fs-14 text-center">Create a secure password for your account.</p>
                        </div>

                        <div class="mb-10">
                            <label class="fs-14 fw-500 text-start mb-3">Enter Password</label>
                            <input id="password" type="password" placeholder="Password"
                                class="form-control mb-5 bg-transparent @error('password') is-invalid @enderror"
                                name="password" required autocomplete="new-password">
                            @error('password')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                            <span id="toggle-password"
                                class=" btn-sm btn-icon position-absolute translate-middle mb-8 end-0 pb-20 pe-2">
                                <i class="fa-solid fa-eye"></i>
                                <i class="fa-solid fa-eye-slash d-none"></i>
                            </span>

                            <!-- Forgot Password Link (hidden by default, shown only in login scenario) -->
                            <div class="d-flex justify-content-end mb-3">
                                <a href="{{ route('password.request') }}" class="blue-text forgot-password-link" style="display: none;">Forgot Password?</a>
                            </div>

                            <!-- <button type="button" class="blue-btn fs-6 fw-600" value="Submit"> Login</button> -->

                        </div>

                        <button class="set-password-btn blue-btn fs-6 fw-600 w-100 d-block"> Register</button>

                    </fieldset>

                </form>

                <div class="site_logo">
                    <a href="{{ url('/') }}" class="text-center">
                        <img src="{{ asset('website') }}/assets/images/header_primary.svg" alt="icon">
                        <h4 class="blue-text pt-2"> Stylenest </h4>
                    </a>
                    <ul>
                        <li> <a href="{{ 'terms' }}" class="blue-text">Terms of Use</a></li>
                        <li><a href="{{ '#!' }}" class="blue-text">Support</a></li>
                        <li><a href="{{ 'privacy_policy' }}" class="blue-text">Privacy policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="col-md-6 login-side-image">
                <img src="{{ asset('website') }}/assets/images/login-banner.png" alt="icon">
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>

    <script>
        $(document).ready(function () {
            // Prevent form submission on Enter key press
            $('.register-form').on('keypress', function (e) {
                if (e.which === 13 || e.keyCode === 13) {
                    e.preventDefault();
                    return false;
                }
            });

            // Also prevent Enter key on individual input fields
            $('.register-form input').on('keypress', function (e) {
                if (e.which === 13 || e.keyCode === 13) {
                    e.preventDefault();
                    return false;
                }
            });
            $('#toggle-password').on('click', function () {
                var passwordField = $('#password');
                var passwordFieldType = passwordField.attr('type');

                if (passwordFieldType === 'password') {
                    passwordField.attr('type', 'text');
                    $(this).find('.fa-eye-slash').removeClass('d-none');
                    $(this).find('.fa-eye').addClass('d-none');
                } else {
                    passwordField.attr('type', 'password');
                    $(this).find('.fa-eye').removeClass('d-none');
                    $(this).find('.fa-eye-slash').addClass('d-none');
                }
            });

            $('#toggle-password-otp').on('click', function () {
                var passwordField = $('#otp');
                var passwordFieldType = passwordField.attr('type');

                if (passwordFieldType === 'password') {
                    passwordField.attr('type', 'text');
                    $(this).find('.fa-eye-slash').removeClass('d-none');
                    $(this).find('.fa-eye').addClass('d-none');
                } else {
                    passwordField.attr('type', 'password');
                    $(this).find('.fa-eye').removeClass('d-none');
                    $(this).find('.fa-eye-slash').addClass('d-none');
                }
            });

            var current_fs, next_fs, previous_fs; // Fieldsets
            var opacity;
            var current = 1;
            var steps = $("fieldset").length;
            var selectedUserType = ''; // Track selected user type

            setProgressBar(current);
            togglePreviousButton(current);

            // Show back button when user starts typing email on step 2
            $(document).on('input', '#email', function() {
                if ($('.step-2').is(':visible')) {
                    togglePreviousButton(2);
                }
            });

            function next_step(element) {
                current_fs = element.closest('fieldset');
                next_fs = current_fs.next('fieldset');

                next_fs.show();
                current_fs.animate({
                    opacity: 0
                }, {
                    step: function (now) {
                        opacity = 1 - now;
                        current_fs.css({
                            'display': 'none',
                            'position': 'relative'
                        });
                        next_fs.css({
                            'opacity': opacity
                        });
                    },
                    duration: 500,
                    complete: function () {
                        // Start timer if we're now on step 3 (OTP step)
                        if (next_fs.hasClass('step-3')) {
                            startOtpTimer();
                        }
                    }
                });
                setProgressBar(++current);
                togglePreviousButton(current);
            }

            // Handle user type card selection
            $(document).on("click", ".user-type-card", function () {
                // Remove active class from all cards
                $('.user-type-card').removeClass('active');

                // Add active class to clicked card
                $(this).addClass('active');

                // Check the radio button
                $(this).find('input[type="radio"]').prop('checked', true);

                // Get selected user type and update content
                selectedUserType = $(this).find('input[type="radio"]').val();
                console.log('Selected user type:', selectedUserType); // Debug log
                updateStep2Content(selectedUserType);

                // Move to next step
                next_step($(this));
            });

            $(".previous").click(function () {
                current_fs = $("fieldset:visible");

                // If we're on step 4 and it's a login scenario, go back to step 2 (skip step 3)
                if (current_fs.hasClass('step-4') && currentScenario === 'login') {
                    previous_fs = current_fs.prev('fieldset').prev('fieldset'); // Go back 2 steps
                    current -= 2; // Adjust current step counter
                } else {
                    previous_fs = current_fs.prev('fieldset');
                    current--;
                }

                previous_fs.show();
                current_fs.animate({
                    opacity: 0
                }, {
                    step: function (now) {
                        opacity = 1 - now;
                        current_fs.css({
                            'display': 'none',
                            'position': 'relative'
                        });
                        previous_fs.css({
                            'opacity': opacity
                        });
                    },
                    duration: 500,
                    complete: function () {
                        // Clear timer if leaving step 3 (OTP step)
                        if (current_fs.hasClass('step-3')) {
                            if (otpTimer) {
                                clearInterval(otpTimer);
                                otpTimer = null;
                            }
                        }
                    }
                });
                setProgressBar(current);
                togglePreviousButton(current);
            });

            function setProgressBar(curStep) {
                var percent = parseFloat(100 / steps) * curStep;
                percent = percent.toFixed();
                $(".progress-bar").css("width", percent + "%");
            }

            function togglePreviousButton(curStep) {
                if (curStep === 1) {
                    $(".previous").hide();
                } else if (curStep === 2) {
                    // On step 2, only show back button if email has been entered
                    const email = $("#email").val();
                    if (email && email.trim() !== '') {
                        $(".previous").show();
                    } else {
                        $(".previous").hide();
                    }
                } else {
                    $(".previous").show();
                }
            }

            // Function to update step 2 content based on selected user type
            function updateStep2Content(userType) {
                if (userType === 'customer') {
                    $('.step-2-title').text('Stylenest for customers');
                    $('.step-2-description').text('Create an account or log in to book salons and spas near you.');
                } else if (userType === 'professional') {
                    $('.step-2-title').text('Stylenest for professionals');
                    $('.step-2-description').text('Create an account or log in to manage your business.');
                }
            }

            $(".submit").click(function () {
                return false;
            });

            // Send OTP Code / Check Email
            let debounceTimeout;
            let currentScenario = 'register'; // Track current scenario
            $(document).on("click", ".send-otp-btn", function () {
                clearTimeout(debounceTimeout);
                const element = $(this);
                debounceTimeout = setTimeout(() => {
                    const email = $("#email").val();
                    // Get the selected user type from the previous step
                    if (!selectedUserType) {
                        selectedUserType = $("input[name='user_type']:checked").val();
                    }

                    if (!email) {
                        Swal.fire({
                            title: "Error",
                            text: "Please enter your email address.",
                            icon: "error"
                        });
                        return;
                    }

                    if (!selectedUserType) {
                        Swal.fire({
                            title: "Error",
                            text: "Please go back and select account type first.",
                            icon: "error"
                        });
                        return;
                    }

                    // Update dynamic email display
                    $('.dynamic-email').text(email);

                    // Disable button & add spinner
                    element.prop("disabled", true);
                    const originalHtml = element.html();
                    element.html(
                        `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Checking...`
                    );

                    // Prepare data with invitation parameters if they exist
                    var requestData = {
                        email: email,
                        user_type: selectedUserType
                    };

                    @if(request('invited_by'))
                        requestData.invited_by = '{{ request('invited_by') }}';
                        requestData.invitation_email = '{{ request('email') }}';
                    @endif

                    $.ajax({
                        url: "{{ route('send_otp') }}",
                        type: "GET",
                        data: requestData,
                        success: function (response) {
                            if (response.status == true) {
                                currentScenario = response.data.scenario;

                                if (response.data.scenario === 'login') {
                                    // Email exists - skip to password step
                                    updatePasswordStepForLogin(selectedUserType);
                                    // Skip OTP step and go directly to password step
                                    skipToPasswordStep(element);
                                } else {
                                    // Email doesn't exist - proceed with OTP verification
                                    updatePasswordStepForRegister(selectedUserType);
                                    next_step(element);
                                }
                            } else {
                                Swal.fire({
                                    title: "Error",
                                    text: response.message,
                                    icon: "error"
                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            console.log(error);
                        },
                        complete: function () {
                            // Re-enable button & restore text
                            element.prop("disabled", false);
                            element.html(originalHtml);
                        }
                    });
                }, 300); // debounce delay 300ms
            });

            // Function to skip to password step (for login scenario)
            function skipToPasswordStep(element) {
                current_fs = element.closest('fieldset');
                // Skip step 3 (OTP) and go directly to step 4 (password)
                next_fs = current_fs.next('fieldset').next('fieldset');

                next_fs.show();
                current_fs.animate({
                    opacity: 0
                }, {
                    step: function (now) {
                        opacity = 1 - now;
                        current_fs.css({
                            'display': 'none',
                            'position': 'relative'
                        });
                        next_fs.css({
                            'opacity': opacity
                        });
                    },
                    duration: 500
                });
                current += 2; // Skip 2 steps
                setProgressBar(current);
                togglePreviousButton(current);
            }

            // Function to update password step for login
            function updatePasswordStepForLogin(userType) {
                const userTypeText = userType === 'customer' ? 'customer' : 'professional';
                $('.step-4 h3').text('Welcome back!');
                $('.step-4 p').text(`Please enter your password to log in as a ${userTypeText}.`);
                $('.set-password-btn').text('Login');
                // Show forgot password link for login scenario
                $('.forgot-password-link').show();
            }

            // Function to update password step for registration
            function updatePasswordStepForRegister(userType) {
                const userTypeText = userType === 'customer' ? 'customer' : 'professional';
                $('.step-4 h3').text('Set Your Password');
                $('.step-4 p').text(`Create a secure password for your ${userTypeText} account.`);
                $('.set-password-btn').text('Register');
                // Hide forgot password link for registration scenario
                $('.forgot-password-link').hide();
            }
            // Send OTP Code End

            // Verify OTP
            let verifyDebounceTimeout;
            $(document).on("click", ".verify-otp-btn", function () {
                clearTimeout(verifyDebounceTimeout);
                const element = $(this);

                verifyDebounceTimeout = setTimeout(() => {
                    const otp = $("#otp").val();
                    if (!otp) {
                        Swal.fire({
                            title: "Error",
                            text: "Please enter the OTP.",
                            icon: "error",
                        });
                        return;
                    }

                    element.prop("disabled", true);
                    let selectedUserType = $("input[name='user_type']:checked").val() || "";
                    const originalHtml = element.html();
                    element.html(
                        `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Verifying...`
                    );

                    // Prepare data with invitation parameters if they exist
                    var verifyData = {
                        _token: "{{ csrf_token() }}",
                        otp: otp,
                        email: $("#email").val(),
                        user_type: selectedUserType
                    };

                    @if(request('invited_by'))
                        verifyData.invited_by = '{{ request('invited_by') }}';
                        verifyData.invitation_email = '{{ request('email') }}';
                    @endif

                    $.ajax({
                        url: "{{ route('verify_otp') }}",
                        type: "POST",
                        data: verifyData,
                        success: function (response) {
                            if (response.status === true) {
                                Swal.fire({
                                    title: "OTP Verified",
                                    text: "Your OTP has been successfully verified. A temporary password has been sent to your email. Please check your inbox and follow the instructions to complete your setup.",
                                    icon: "success",
                                    willClose: () => {
                                        // Redirect after alert is closed
                                        window.location.href = response.data.url;
                                    }
                                });
                            } else {
                                Swal.fire({
                                    title: "Error",
                                    text: response.message,
                                    icon: "error"
                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            console.log(error);
                        },
                        complete: function () {
                            element.prop("disabled", false);
                            element.html(originalHtml);
                        }
                    });

                }, 300);
            });
            // Verify OTP End

            // OTP Timer functionality
            let otpTimer;
            let timeLeft = 120; // 2 minutes in seconds

            function startOtpTimer() {
                // Clear any existing timer first
                if (otpTimer) {
                    clearInterval(otpTimer);
                }

                timeLeft = 120; // Reset to 2 minutes
                updateTimerDisplay();

                // Reset button to verify state
                $('.resend-otp-btn').removeClass('resend-otp-btn')
                    .addClass('blue-btn verify-otp-btn').text('Verify OTP')
                    .css({
                        'background-color': '',
                        'border': '',
                        'color': '',
                        'transition': ''
                    })
                    .off('mouseenter mouseleave'); // Remove hover events

                otpTimer = setInterval(function () {
                    timeLeft--;
                    updateTimerDisplay();

                    if (timeLeft <= 0) {
                        clearInterval(otpTimer);
                        // Change button to Resend Code with better styling
                        $('.verify-otp-btn').removeClass('blue-btn')
                            .text('Resend Code').removeClass('verify-otp-btn').addClass('resend-otp-btn')
                            .css({
                                'background-color': '#f8f9fa',
                                'border': '1px solid #dee2e6',
                                'color': '#495057',
                                'transition': 'all 0.2s ease-in-out'
                            })
                            .hover(
                                function () {
                                    $(this).css({
                                        'background-color': '#020C87',
                                        'border-color': '#adb5bd',
                                        'color': '#FFF'
                                    });
                                },
                                function () {
                                    $(this).css({
                                        'background-color': '#020C87',
                                        'border-color': '#dee2e6',
                                        'color': '#FFF'
                                    });
                                }
                            );
                        $('#otp-timer').text('00:00').removeClass('text-danger').addClass('text-muted');
                    }
                }, 1000);
            }

            function updateTimerDisplay() {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                $('#otp-timer').text(display);

                // Update color based on time left
                if (timeLeft <= 30) {
                    $('#otp-timer').removeClass('text-danger').addClass('text-warning');
                } else if (timeLeft <= 0) {
                    $('#otp-timer').removeClass('text-warning text-danger').addClass('text-muted');
                } else {
                    $('#otp-timer').removeClass('text-warning text-muted').addClass('text-danger');
                }
            }

            // Start timer when OTP step is shown (step 3)
            function startTimerIfOtpStep() {
                if ($('.step-3').is(':visible')) {
                    startOtpTimer();
                }
            }

            // Resend OTP functionality
            $(document).on("click", ".resend-otp-btn", function () {
                const element = $(this);
                const email = $("#email").val();

                if (!email) {
                    Swal.fire({
                        title: "Error",
                        text: "Email is required to resend OTP.",
                        icon: "error",
                    });
                    return;
                }

                element.prop("disabled", true);
                const originalHtml = element.html();
                element.html(
                    `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Resending...`
                );

                $.ajax({
                    url: "{{ route('resend_otp') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        email: email
                    },
                    success: function (response) {
                        if (response.status === true) {
                            Swal.fire({
                                title: "OTP Resent",
                                text: "A new OTP has been sent to your email.",
                                icon: "success"
                            });

                            // Reset button back to Verify OTP and restart timer
                            element.removeClass('resend-otp-btn')
                                .addClass('blue-btn verify-otp-btn')
                                .text('Verify OTP')
                                .css({
                                    'background-color': '',
                                    'border': '',
                                    'color': '',
                                    'transition': ''
                                })
                                .off('mouseenter mouseleave'); // Remove hover events
                            $('#otp').val(''); // Clear OTP input
                            startOtpTimer(); // Restart timer
                        } else {
                            Swal.fire({
                                title: "Error",
                                text: response.message,
                                icon: "error"
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        Swal.fire({
                            title: "Error",
                            text: "Failed to resend OTP. Please try again.",
                            icon: "error"
                        });
                    },
                    complete: function () {
                        element.prop("disabled", false);
                        if (element.hasClass('resend-otp-btn')) {
                            element.html(originalHtml);
                        }
                    }
                });
            });

            // Set Password
            $(document).on("click", ".set-password-btn", function () {
                const element = $(this);
                const password = $("#password").val();
                if (!password) {
                    Swal.fire({
                        title: "Error",
                        text: "Please enter the password.",
                        icon: "error",
                    });
                    return;
                }

                element.prop("disabled", true);
                const originalHtml = element.html();
                const loadingText = currentScenario === 'login' ? 'Logging in...' : 'Setting...';
                element.html(
                    `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ${loadingText}`
                );

                var customerRegisterUrl = "{{ route('register.user_type', ['user_type' => 'customer']) }}";
                var professionalRegisterUrl =
                    "{{ route('register.user_type', ['user_type' => 'professional']) }}";

                $.ajax({
                    url: "{{ route('set_password') }}",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        password: password,
                        email: $("#email").val(),
                        scenario: currentScenario,
                        user_type: selectedUserType
                    },
                    success: function (response) {
                        if (response.status === true) {
                            var userType = response.role;

                            if (currentScenario === 'login') {
                                // For login, redirect based on user role
                                if (userType == "customer") {
                                    window.location.href = "/"; // Customer goes to home page
                                } else if (userType == "developer") {
                                    window.location.href = "/home"; // Developer goes to /home
                                } else if (userType == "professional" || userType == "admin" || userType == "business" || userType == "individual") {
                                    window.location.href = "/dashboard"; // Professional/admin/business/individual dashboard
                                } else {
                                    window.location.href = "/dashboard"; // Default dashboard
                                }
                            } else {
                                // For registration, continue to registration flow
                                if (userType == "customer") {
                                    window.location.href = customerRegisterUrl;
                                } else if (userType == "professional") {
                                    window.location.href = professionalRegisterUrl;
                                } else {
                                    window.location.href = "/dashboard"; // Individual/business dashboard
                                }
                            }
                        } else {
                            Swal.fire({
                                title: "Error",
                                text: response.message,
                                icon: "error"
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                    },
                    complete: function () {
                        element.prop("disabled", false);
                        element.html(originalHtml);
                    }
                });
            });
            // Set Password End

            $(document).on("click", ".google-login-btn, .apple-login-btn", function (e) {
                e.preventDefault();
                const selectedUserType = $("input[name='user_type']:checked").val();

                if (!selectedUserType) {
                    Swal.fire({
                        title: "Error",
                        text: "Please select account type first (customer or professional).",
                        icon: "error"
                    });
                    return;
                }

                // Update href with selected user type
                const href = $(this).attr('href').split('/');
                href[href.length - 1] = selectedUserType;
                window.location.href = href.join('/');
            });
        });
    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Check for session values
            @if(session('skip_to_step') == '2')
                console.log("Session has skip_to_step = 2");

                // Force hide all fieldsets
                const allFieldsets = document.querySelectorAll('fieldset');
                allFieldsets.forEach(fieldset => {
                    fieldset.style.display = 'none';
                });

                // Force show step 2
                const step2 = document.querySelector('.step-2');
                if (step2) {
                    step2.style.display = 'block';
                    step2.style.opacity = '1';

                    // Set the email value if provided
                    @if(session('social_email'))
                        const emailInput = document.getElementById('email');
                        if (emailInput) {
                            emailInput.value = "{{ session('social_email') }}";
                        }
                    @endif

                        // Set the user type if provided
                        @if(session('social_user_type'))
                            const userTypeInput = document.querySelector('input[name="user_type"][value="{{ session('social_user_type') }}"]');
                            if (userTypeInput) {
                                userTypeInput.checked = true;

                                // If you have a visual indicator for selection
                                const userTypeCards = document.querySelectorAll('.user-type-card');
                                userTypeCards.forEach(card => card.classList.remove('active'));
                                userTypeInput.closest('.user-type-card').classList.add('active');
                            }
                        @endif

                    // Set global variables for the progress bar
                    window.current = 2;

                    // Try to update the progress bar
                    setTimeout(function () {
                        if (typeof setProgressBar === 'function') {
                            setProgressBar(2);
                        }

                        if (typeof togglePreviousButton === 'function') {
                            togglePreviousButton(2);
                        }

                        @if(session('social_user_type'))
                            if (typeof updateStep2Content === 'function') {
                                updateStep2Content("{{ session('social_user_type') }}");
                            }
                        @endif
                                                        }, 100);
                }
            @endif
                        });
    </script>

    <script>
        $(document).ready(function () {
            // Replace '#stepperForm' with your actual form ID
            $('#register-stepper-form').on('keypress', function (e) {
                if (e.which === 13) {
                    e.preventDefault();
                    return false;
                }
            });

            // Handle invitation flow - auto-select customer and proceed if invited
            @if(request('invited_by'))
                // Auto-select customer type
                selectedUserType = 'customer';
                $('.user-type-card[data-user-type="customer"]').addClass('active');

                // Auto-proceed to next step after a short delay
                setTimeout(function() {
                    $('.next.action-button').click();
                }, 500);
            @endif
        });
    </script>



@endpush

<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class WalletCustomersExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    protected $customers;

    public function __construct($customers)
    {
        $this->customers = $customers;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->customers;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Customer Name',
            'Email Address',
            'Total Bookings',
            'Completed Bookings',
            'Total Spent',
            'Join Date'
        ];
    }

    /**
     * @param mixed $customer
     * @return array
     */
    public function map($customer): array
    {
        $totalBookings = $customer->bookings->count();
        $completedBookings = $customer->bookings->where('status', 1)->count();
        $totalSpent = $customer->bookings->where('status', 1)->sum('total_amount');
        $joinDate = \Carbon\Carbon::parse($customer->created_at)->format('M d, Y');

        return [
            $customer->name ?? 'N/A',
            $customer->email ?? 'N/A',
            $totalBookings,
            $completedBookings,
            '$' . number_format($totalSpent, 2),
            $joinDate
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\Notification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;

class ThemeController extends Controller
{
    function __construct() {}
    public function permissions()
    {
        return view('theme.user-management.permissions');
    }

    public function professional_account()
    {
        return view('auth.professional_account_stepper');
    }

    public function dashboard()
    {
        return view('dashboard.index');
    }
    public function testing()
    {
        return view('dashboard.customer.testing');
    }
    public function familyFriends()
    {
        return view('dashboard.customer.family-friends');
    }
    public function addFriends()
    {
        return view('dashboard.customer.add-friend');
    }
    public function friendsDetails()
    {
        return view('dashboard.customer.friends-family-detail');
    }
    public function customerWallet()
    {
        return view('dashboard.customer.customer-wallet');
    }

    public function cart()
    {
        // return session()->all();
         $cards = collect(session()->get('booking_cards'));
        return view('dashboard.customer.cart', compact('cards'));
    }

    public function removeCartItem(Request $request)
    {
        try {
            $serviceId = $request->input('service_id');
            $bookingDate = $request->input('booking_date');
            $bookingTime = $request->input('booking_time');

            // Get current cart items from session
            $cartItems = session()->get('booking_cards', []);

            // Find and remove the matching item
            $updatedCart = array_filter($cartItems, function($item) use ($serviceId, $bookingDate, $bookingTime) {
                return !(
                    $item['service']['ids'] == $serviceId &&
                    $item['booking_date'] == $bookingDate &&
                    $item['booking_time'] == $bookingTime
                );
            });

            // Re-index the array to maintain proper indexing
            $updatedCart = array_values($updatedCart);

            // Update session with the filtered cart
            session()->put('booking_cards', $updatedCart);

            return response()->json([
                'status' => true,
                'message' => 'Item removed from cart successfully',
                'cart_count' => count($updatedCart)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to remove item from cart: ' . $e->getMessage()
            ], 500);
        }
    }

    public function validateCoupon(Request $request)
    {
        try {
            $couponCode = $request->input('coupon_code');

            if (empty($couponCode)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Please enter a coupon code'
                ]);
            }

            // Get current cart items from session
            $cartItems = session()->get('booking_cards', []);

            if (empty($cartItems)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Your cart is empty'
                ]);
            }

            // Calculate current cart total
            $cartTotal = collect($cartItems)->sum(function($item) {
                return $item['service']['price'] ?? 0;
            });

            // Here you can add your coupon validation logic
            // For now, I'll create a simple example with some predefined coupons
            $validCoupons = [
                'SAVE10' => ['type' => 'percentage', 'value' => 10, 'min_amount' => 50],
                'SAVE20' => ['type' => 'percentage', 'value' => 20, 'min_amount' => 100],
                'FLAT15' => ['type' => 'fixed', 'value' => 15, 'min_amount' => 30],
                'WELCOME' => ['type' => 'percentage', 'value' => 15, 'min_amount' => 0],
            ];

            if (!isset($validCoupons[$couponCode])) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid coupon code'
                ]);
            }

            $coupon = $validCoupons[$couponCode];

            // Check minimum amount requirement
            if ($cartTotal < $coupon['min_amount']) {
                return response()->json([
                    'status' => false,
                    'message' => "Minimum order amount of $" . $coupon['min_amount'] . " required for this coupon"
                ]);
            }

            // Calculate discount
            $discount = 0;
            if ($coupon['type'] === 'percentage') {
                $discount = ($cartTotal * $coupon['value']) / 100;
            } else {
                $discount = $coupon['value'];
            }

            // Ensure discount doesn't exceed cart total
            $discount = min($discount, $cartTotal);
            $newTotal = $cartTotal - $discount;

            // Store coupon in session
            session()->put('applied_coupon', [
                'code' => $couponCode,
                'discount' => $discount,
                'original_total' => $cartTotal,
                'new_total' => $newTotal
            ]);

            return response()->json([
                'status' => true,
                'message' => 'Coupon applied successfully!',
                'discount' => $discount,
                'new_total' => $newTotal,
                'original_total' => $cartTotal
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'An error occurred while validating the coupon: ' . $e->getMessage()
            ], 500);
        }
    }

    public function notification()
    {
        $user = auth()->user();
        $notifications = Notification::where('user_id', $user->id)->with('user')->latest()->get();
        return view('dashboard.notification', compact('notifications'));
    }

    public function getUnreadNotificationCount()
    {
        $user = auth()->user();
        $count = Notification::where('user_id', $user->id)
            ->where(function($query) {
                $query->where('read', false)
                      ->orWhereNull('read');
            })
            ->count();

        return response()->json([
            'success' => true,
            'count' => $count
        ]);
    }

    public function markAllAsRead()
    {
        $user = auth()->user();
        Notification::where('user_id', $user->id)
            ->where(function($query) {
                $query->where('read', false)
                      ->orWhereNull('read');
            })
            ->update(['read' => true]);

        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read'
        ]);
    }

    public function markAsRead($id)
    {
        $user = auth()->user();
        $notification = Notification::where('user_id', $user->id)->where('id', $id)->first();

        if ($notification) {
            $notification->read = true;
            $notification->save();

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as read'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Notification not found'
        ]);
    }
    //    business controller
    public function businessAnalytics(Request $request)
    {
        $user = auth()->user();
        $userId = $user->id;
        $period = $request->get('period', 'weekly'); // Default to weekly

        // Calculate analytics data based on period
        if ($period === 'monthly') {
            // Monthly data (current month by weeks)
            $data = $this->getMonthlyAnalyticsData($userId);
        } else {
            // Weekly data (last 7 days)
            $data = $this->getWeeklyAnalyticsData($userId);
        }

        $data['selectedPeriod'] = $period;

        return view('dashboard.business.analytics', $data);
    }

    private function getWeeklyAnalyticsData($userId)
    {
        // Get weekly data (last 7 days)
        $weeklyLabels = [];
        $peakBookingData = [];
        $weeklyUpcoming = [];
        $weeklyCompleted = [];

        $totalSales = 0;
        $lastWeekSales = 0;
        $totalBookings = 0;

        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $weeklyLabels[] = $date->format('D');

            // Peak booking times (total bookings per day)
            $dayBookings = Booking::where('provider_id', $userId)
                ->whereDate('booking_date', $date->toDateString())
                ->where('status', '!=', 2) // Exclude cancelled
                ->count();
            $peakBookingData[] = $dayBookings;

            // Upcoming bookings count
            $upcomingCount = Booking::where('provider_id', $userId)
                ->where('status', 0)
                ->whereDate('booking_date', $date->toDateString())
                ->where('booking_date', '>=', now()->toDateString())
                ->count();
            $weeklyUpcoming[] = $upcomingCount;

            // Completed bookings count
            $completedCount = Booking::where('provider_id', $userId)
                ->where('status', 1)
                ->whereDate('booking_date', $date->toDateString())
                ->count();
            $weeklyCompleted[] = $completedCount;

            // Sales data
            $daySales = Booking::where('provider_id', $userId)
                ->where('status', 1)
                ->whereDate('booking_date', $date->toDateString())
                ->sum('total_amount');
            $totalSales += $daySales;
        }

        // Last week sales for comparison
        for ($i = 13; $i >= 7; $i--) {
            $date = now()->subDays($i);
            $lastWeekSales += Booking::where('provider_id', $userId)
                ->where('status', 1)
                ->whereDate('booking_date', $date->toDateString())
                ->sum('total_amount');
        }

        // Calculate total bookings for the week
        $totalBookings = Booking::where('provider_id', $userId)
            ->whereBetween('booking_date', [now()->subDays(6)->toDateString(), now()->toDateString()])
            ->where('status', '!=', 2)
            ->count();

        // Calculate percentage changes
        $salesChange = $lastWeekSales > 0 ? round((($totalSales - $lastWeekSales) / $lastWeekSales) * 100, 1) : 0;

        return [
            'totalSales' => $totalSales,
            'salesChange' => $salesChange,
            'fromLastPeriod' => $lastWeekSales,
            'totalBookings' => $totalBookings,
            'peakBookingLabels' => $weeklyLabels,
            'peakBookingData' => $peakBookingData,
            'upcomingBookings' => array_sum($weeklyUpcoming),
            'completedBookings' => array_sum($weeklyCompleted),
            'upcomingData' => $weeklyUpcoming,
            'completedData' => $weeklyCompleted,
            'periodDescription' => 'Last 7 days'
        ];
    }

    private function getMonthlyAnalyticsData($userId)
    {
        // Get monthly data (current month by weeks)
        $monthlyLabels = [];
        $peakBookingData = [];
        $monthlyUpcoming = [];
        $monthlyCompleted = [];

        $totalSales = 0;
        $lastMonthSales = 0;
        $totalBookings = 0;

        $startOfMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        // Group by weeks for better visualization
        for ($week = 1; $week <= 4; $week++) {
            $weekStart = $startOfMonth->copy()->addWeeks($week - 1);
            $weekEnd = $weekStart->copy()->addDays(6);

            if ($weekEnd->gt($endOfMonth)) {
                $weekEnd = $endOfMonth->copy();
            }

            $monthlyLabels[] = 'Week ' . $week;

            // Peak booking times (total bookings per week)
            $weekBookings = Booking::where('provider_id', $userId)
                ->whereBetween('booking_date', [$weekStart->toDateString(), $weekEnd->toDateString()])
                ->where('status', '!=', 2) // Exclude cancelled
                ->count();
            $peakBookingData[] = $weekBookings;

            // Upcoming bookings count
            $upcomingCount = Booking::where('provider_id', $userId)
                ->where('status', 0)
                ->whereBetween('booking_date', [$weekStart->toDateString(), $weekEnd->toDateString()])
                ->where('booking_date', '>=', now()->toDateString())
                ->count();
            $monthlyUpcoming[] = $upcomingCount;

            // Completed bookings count
            $completedCount = Booking::where('provider_id', $userId)
                ->where('status', 1)
                ->whereBetween('booking_date', [$weekStart->toDateString(), $weekEnd->toDateString()])
                ->count();
            $monthlyCompleted[] = $completedCount;

            // Sales data
            $weekSales = Booking::where('provider_id', $userId)
                ->where('status', 1)
                ->whereBetween('booking_date', [$weekStart->toDateString(), $weekEnd->toDateString()])
                ->sum('total_amount');
            $totalSales += $weekSales;
        }

        // Last month sales for comparison
        $lastMonthStart = now()->subMonth()->startOfMonth();
        $lastMonthEnd = now()->subMonth()->endOfMonth();
        $lastMonthSales = Booking::where('provider_id', $userId)
            ->where('status', 1)
            ->whereBetween('booking_date', [$lastMonthStart->toDateString(), $lastMonthEnd->toDateString()])
            ->sum('total_amount');

        // Calculate total bookings for the month
        $totalBookings = Booking::where('provider_id', $userId)
            ->whereBetween('booking_date', [$startOfMonth->toDateString(), $endOfMonth->toDateString()])
            ->where('status', '!=', 2)
            ->count();

        // Calculate percentage changes
        $salesChange = $lastMonthSales > 0 ? round((($totalSales - $lastMonthSales) / $lastMonthSales) * 100, 1) : 0;

        return [
            'totalSales' => $totalSales,
            'salesChange' => $salesChange,
            'fromLastPeriod' => $lastMonthSales,
            'totalBookings' => $totalBookings,
            'peakBookingLabels' => $monthlyLabels,
            'peakBookingData' => $peakBookingData,
            'upcomingBookings' => array_sum($monthlyUpcoming),
            'completedBookings' => array_sum($monthlyCompleted),
            'upcomingData' => $monthlyUpcoming,
            'completedData' => $monthlyCompleted,
            'periodDescription' => 'This month'
        ];
    }

    public function businessEarning()
    {
        $baseQuery = Booking::query();

        // Apply role-based filtering
        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $baseQuery->where('provider_id', auth()->id());
        }

        // Total Earnings (completed bookings)
        $totalEarnings = (clone $baseQuery)
            ->where('status', 1)
            ->sum('total_amount');

        // Payment Being Cleared (pending bookings - status 0)
        $paymentBeingCleared = (clone $baseQuery)
            ->where('status', 0)
            ->sum('total_amount');

        // Get all bookings for the table with relationships
        $bookings = (clone $baseQuery)
            ->with(['service', 'service.category', 'customer'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Get categories for the filter dropdown
        $categories = \App\Models\Category::active()->get();

        // Calculate percentage changes (mock data for now)
        $earningsStats = [
            'total_earnings' => $totalEarnings,
            'total_earnings_change' => 17.2, // Mock percentage
            'payment_being_cleared' => $paymentBeingCleared,
            'payment_being_cleared_change' => 17.2, // Mock percentage
        ];

        return view('dashboard.business.earning', compact('bookings', 'earningsStats', 'categories'));
    }

    public function filterEarnings(Request $request)
    {
        $baseQuery = Booking::query();

        // Apply role-based filtering
        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $baseQuery->where('provider_id', auth()->id());
        }

        $query = (clone $baseQuery)->with(['service', 'service.category', 'customer']);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('customer', function ($customerQuery) use ($search) {
                    $customerQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhereHas('service', function ($serviceQuery) use ($search) {
                    $serviceQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhereHas('service.category', function ($categoryQuery) use ($search) {
                    $categoryQuery->where('name', 'like', "%{$search}%");
                });
            });
        }

        // Apply status filter
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $status = $request->get('status');
            switch ($status) {
                case 'ongoing':
                    $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) < ?", [now()]);
                    break;
                case 'upcoming':
                    $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) >= ?", [now()]);
                    break;
                case 'complete':
                    $query->where('status', 1);
                    break;
                case 'canceled':
                    $query->where('status', 2);
                    break;
            }
        }

        // Apply category filter
        if ($request->filled('category') && $request->get('category') !== 'Category') {
            $query->whereHas('service.category', function ($categoryQuery) use ($request) {
                $categoryQuery->where('name', $request->get('category'));
            });
        }

        $bookings = $query->orderBy('created_at', 'desc')->get();

        // Generate HTML for the table rows
        $html = view('dashboard.business.partials.earning-table-rows', compact('bookings'))->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'count' => $bookings->count()
        ]);
    }

    public function exportEarnings(Request $request)
    {
        $baseQuery = Booking::query();

        // Apply role-based filtering
        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $baseQuery->where('provider_id', auth()->id());
        }

        $query = (clone $baseQuery)->with(['service', 'service.category', 'customer']);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('customer', function ($customerQuery) use ($search) {
                    $customerQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhereHas('service', function ($serviceQuery) use ($search) {
                    $serviceQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhereHas('service.category', function ($categoryQuery) use ($search) {
                    $categoryQuery->where('name', 'like', "%{$search}%");
                });
            });
        }

        // Apply status filter
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $status = $request->get('status');
            switch ($status) {
                case 'ongoing':
                    $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) < ?", [now()]);
                    break;
                case 'upcoming':
                    $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) >= ?", [now()]);
                    break;
                case 'complete':
                    $query->where('status', 1);
                    break;
                case 'canceled':
                    $query->where('status', 2);
                    break;
            }
        }

        // Apply category filter
        if ($request->filled('category') && $request->get('category') !== 'Category') {
            $query->whereHas('service.category', function ($categoryQuery) use ($request) {
                $categoryQuery->where('name', $request->get('category'));
            });
        }

        $bookings = $query->orderBy('created_at', 'desc')->get();

        // Create filename with filter info
        $filename = 'earnings-' . now()->format('Y-m-d');
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $filename .= '-' . $request->get('status');
        }
        if ($request->filled('category') && $request->get('category') !== 'Category') {
            $filename .= '-' . str_replace(' ', '-', strtolower($request->get('category')));
        }
        $filename .= '.xlsx';

        return \Excel::download(new \App\Exports\EarningsExport($bookings), $filename);
    }

    public function staffMemberDetails()
    {
        return view('dashboard.business.staff-member-details');
    }
    // Individual

    // admin

    public function refundRequest()
    {
        return view('dashboard.admin.refund-request');
    }
    public function adminWallet()
    {
        // Get all bookings for admin overview
        $allBookings = Booking::with(['service', 'service.category', 'customer', 'provider'])->get();

        // Calculate wallet statistics
        $paidByCustomers = $allBookings->where('status', 1)->sum('total_amount'); // Completed bookings
        $paidByProfessionals = $allBookings->where('status', 1)->sum('total_amount'); // Same as above for now
        $totalRevenue = $allBookings->where('status', 1)->sum('total_amount'); // Total completed revenue
        $numberOfBookings = $allBookings->count();

        // Get customers with their booking data
        $customers = User::whereHas('roles', function($query) {
            $query->where('name', 'customer');
        })
        ->with(['bookings' => function($query) {
            $query->with(['service', 'service.category']);
        }])
        ->get();

        // Get professionals with their booking data
        $professionals = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['individual', 'business', 'professional']);
        })
        ->with(['providedBookings' => function($query) {
            $query->with(['service', 'service.category', 'customer']);
        }])
        ->get();

        // Get categories for filter dropdown
        $categories = \App\Models\Category::active()->get();

        $walletStats = [
            'paid_by_customers' => $paidByCustomers,
            'paid_by_professionals' => $paidByProfessionals,
            'total_revenue' => $totalRevenue,
            'number_of_bookings' => $numberOfBookings,
        ];

        return view('dashboard.admin.wallet', compact('customers', 'professionals', 'categories', 'walletStats'));
    }

    public function filterWalletCustomers(Request $request)
    {
        $query = User::whereHas('roles', function($q) {
            $q->where('name', 'customer');
        })->with(['bookings' => function($q) {
            $q->with(['service', 'service.category']);
        }]);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Apply status filter (based on booking status)
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $status = $request->get('status');
            $query->whereHas('bookings', function($q) use ($status) {
                switch ($status) {
                    case 'ongoing':
                        $q->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) < ?", [now()]);
                        break;
                    case 'upcoming':
                        $q->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) >= ?", [now()]);
                        break;
                    case 'complete':
                        $q->where('status', 1);
                        break;
                    case 'canceled':
                        $q->where('status', 2);
                        break;
                }
            });
        }

        // Apply category filter
        if ($request->filled('category') && $request->get('category') !== 'Category') {
            $query->whereHas('bookings.service.category', function($q) use ($request) {
                $q->where('name', $request->get('category'));
            });
        }

        $customers = $query->get();

        $html = view('dashboard.admin.partials.wallet-customers-table', compact('customers'))->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'count' => $customers->count()
        ]);
    }

    public function filterWalletProfessionals(Request $request)
    {
        $query = User::whereHas('roles', function($q) {
            $q->whereIn('name', ['individual', 'business', 'professional']);
        })->with(['providedBookings' => function($q) {
            $q->with(['service', 'service.category', 'customer']);
        }]);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Apply status filter (based on booking status)
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $status = $request->get('status');
            $query->whereHas('providedBookings', function($q) use ($status) {
                switch ($status) {
                    case 'ongoing':
                        $q->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) < ?", [now()]);
                        break;
                    case 'upcoming':
                        $q->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) >= ?", [now()]);
                        break;
                    case 'complete':
                        $q->where('status', 1);
                        break;
                    case 'canceled':
                        $q->where('status', 2);
                        break;
                }
            });
        }

        $professionals = $query->get();

        $html = view('dashboard.admin.partials.wallet-professionals-table', compact('professionals'))->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'count' => $professionals->count()
        ]);
    }

    public function exportWalletCustomers()
    {
        $customers = User::whereHas('roles', function($q) {
            $q->where('name', 'customer');
        })->with(['bookings' => function($q) {
            $q->with(['service', 'service.category']);
        }])->get();

        return \Excel::download(new \App\Exports\WalletCustomersExport($customers), 'wallet-customers-' . now()->format('Y-m-d') . '.xlsx');
    }

    public function exportWalletProfessionals()
    {
        $professionals = User::whereHas('roles', function($q) {
            $q->whereIn('name', ['individual', 'business', 'professional']);
        })->with(['providedBookings' => function($q) {
            $q->with(['service', 'service.category', 'customer']);
        }])->get();

        return \Excel::download(new \App\Exports\WalletProfessionalsExport($professionals), 'wallet-professionals-' . now()->format('Y-m-d') . '.xlsx');
    }

    public function adminVat()
    {
        return view('dashboard.admin.vat-mgmt');
    }
}

@forelse ($professionals as $professional)
    <tr>
        <td data-label="PROFESSIONAL NAME">{{ $professional->name ?? 'N/A' }}</td>
        <td data-label="EMAIL ADDRESS">{{ $professional->email ?? 'N/A' }}</td>
        <td data-label="BOOKINGS">{{ $professional->providedBookings->count() }}</td>
        <td data-label="STATUS" class="request-status status paid-status">
            @php
                $completedBookings = $professional->providedBookings->where('status', 1)->count();
                $totalBookings = $professional->providedBookings->count();
            @endphp
            {{ $completedBookings }}/{{ $totalBookings }} Completed
        </td>
        <td data-label="DATE & TIME">
            {{ $professional->created_at ? \Carbon\Carbon::parse($professional->created_at)->format('d M, Y') : 'N/A' }}
            <span class="wallet-time status unpaid-status ps-4">
                {{ $professional->created_at ? \Carbon\Carbon::parse($professional->created_at)->format('g:i A') : '' }}
            </span>
        </td>
        <td data-label="">
            <div class="dropdown">
                <button class="drop-btn" type="button" id="dropdownMenuButton"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <li>
                        <a class="dropdown-item view fs-14 regular" href="{{ route('professional.show', $professional->id) }}">
                            <i class="bi bi-eye view-icon"></i>
                            View Details
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item fs-14 regular" href="{{ route('professional.change_status', $professional->id) }}">
                            <i class="bi bi-toggle-on"></i>
                            Change Status
                        </a>
                    </li>
                </ul>
            </div>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="7" class="text-center">No professionals found</td>
    </tr>
@endforelse

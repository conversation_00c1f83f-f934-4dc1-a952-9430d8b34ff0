<div class="mb-5">
    <div class="col-md-12">
        <label for="clientName" class="form-label form-input-labels">Client Name</label>
        <input type="text" class="form-control form-inputs-field" name="clientName" id="clientName"
               placeholder="Enter client name">
    </div>
    <div class="col-md-12">
        <label for="clientEmail" class="form-label form-input-labels">Email</label>
        <input type="email" class="form-control form-inputs-field" name="clientEmail" id="clientEmail"
               placeholder="Enter client email">
    </div>
    <div class="col-md-12">
        <label for="phone" class="form-label form-input-label">Phone Number</label>
        <div class="form-control form-inputs-field">
            <input type="tel" id="phone" name="phone" placeholder="Enter phone number">
        </div>
    </div>
    <div class="col-md-12">
        <label for="professionalService" class="form-label form-input-labels">Select Service</label>
        <select class="form-select form-select-field" id="professionalService" name="service"
                data-placeholder="Select">
            {{-- <option disabled selected>Select Service</option> --}}
            @foreach ($services as $index => $service)
                <option value="{{ $service->ids }}"
                        @if(isset($service_id) && $service->ids == $service_id) selected @elseif($loop->first) selected @endif>
                    {{ $service->name }}
                </option>
            @endforeach
        </select>
    </div>
</div>

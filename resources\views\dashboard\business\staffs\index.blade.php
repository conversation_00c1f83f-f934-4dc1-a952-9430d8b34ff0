@extends('dashboard.layout.master')

@push('css')
<style>
    .search_input.searching {
        background-image: url("data:image/svg+xml,%3csvg width='20' height='20' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M9 2a7 7 0 1 0 0 14 7 7 0 0 0 0-14zM2 9a7 7 0 1 1 14 0 7 7 0 0 1-14 0z' fill='%23999'/%3e%3cpath d='m13 13 4 4' stroke='%23999' stroke-width='2' stroke-linecap='round'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 12px center;
        animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }

    .dot.all {
        background-color: #4B5563;
    }
</style>
@endpush

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service ">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Staff Members</h6>
                        {{-- <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p> --}}
                    </div>
                    <a href="{{ route('staffs.create') }}" class="add-btn">
                        <i class="fa-solid fa-plus me-3"></i> Add Staff
                    </a>
                </div>
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                            <div class="search_box">
                                <label for="customSearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="customSearchInput"
                                    placeholder="Search by name or email" />
                            </div>
                            <!-- Select with dots -->
                            <div class="dropdown search_box select-box">
                                <button
                                    class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <span><span class="dot"></span>
                                        All</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="All" data-value="all"
                                            data-color="#4B5563"><span class="dot all"></span>
                                            All</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Active" data-value="active"
                                            data-color="#10B981"><span class="dot" style="background-color: #10B981;"></span>
                                            Active</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Inactive" data-value="inactive"
                                            data-color="#EF4444"><span class="dot" style="background-color: #EF4444;"></span>
                                            Inactive</a></li>
                                </ul>
                            </div>
                        </div>
                        <table id="responsiveTable" class="display w-100">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>Email Address</th>
                                    <th>Category</th>
                                    {{-- <th>Bookings</th> --}}
                                    <th>Action</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody id="staffTableBody">
                                @include('dashboard.business.staffs.partials.staff-table', ['staffs' => $staffs])
                            </tbody>
                        </table>
                        <div class="d-flex justify-content-center" id="staffPagination"
                            style="{{ $staffs->hasPages() ? '' : 'display: none;' }}">
                            {{ $staffs->links('pagination::bootstrap-4') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.business.staffs.modal.edit-staff-member')
    @include('dashboard.business.staffs.modal.available-modal')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        // stacked modal in edit
        var elements = Array.prototype.slice.call(document.querySelectorAll("[data-bs-stacked-modal]"));

        if (elements && elements.length > 0) {
            elements.forEach((element) => {
                if (element.getAttribute("data-kt-initialized") === "1") {
                    return;
                }
                element.setAttribute("data-kt-initialized", "1");
                element.addEventListener("click", function(e) {
                    e.preventDefault();

                    const modalEl = document.querySelector(this.getAttribute("data-bs-stacked-modal"));

                    if (modalEl) {
                        const modal = new bootstrap.Modal(modalEl);
                        modal.show();
                    }
                });
            });
        }


        // staff member vacational modal triggerd
        document.querySelectorAll('.toggle-container').forEach(function(container) {
            container.addEventListener('click', function(e) {
                e.stopPropagation(); // Prevent dropdown from closing
            });
        });

        // Handle toggle label update and modal trigger
        document.querySelectorAll('.toggle-input-vacation').forEach(function(toggleInput) {
            const container = toggleInput.closest('.toggle-container');
            const toggleLabel = container.querySelector('.toggle-label-vacation');

            toggleInput.addEventListener('change', function() {
                const isOn = toggleInput.checked;
                toggleLabel.textContent = isOn ? 'Vacation On' : 'Vacation Off';

                // Optional: Show modal when vacation is turned ON
                if (isOn) {
                    const modalEl = document.getElementById('vacationModal');
                    console.log("modal opened")
                    if (modalEl) {
                        const modal = new bootstrap.Modal(modalEl);
                        modal.show();
                    }
                }
            });
        });
    </script>

    <script>
        $('.staff-toggle').off('change').on('change', function() {
            const staffId = $(this).data('staff-id');
            const status = $(this).is(':checked') ? 1 : 0;

            $.ajax({
                url: '{{ route('staffs.update-status') }}',
                method: 'POST',
                data: {
                    staff_id: staffId,
                    status: status,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        // Update toggle label
                        const label = status ? 'Active' : 'Deactive';
                        $(this).siblings('.toggle-label').text(label);
                    }
                }.bind(this)
            });
        });

        $(document).ready(function() {
            // Add validation CSS
            $('<style>.error { color: #fd011a !important; font-weight: bold !important; font-size: 12px; margin-top: 5px; display: block; }</style>')
                .appendTo('head');

            // Custom validation methods
            $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                if (element.files.length === 0) {
                    return true;
                }
                const fileSizeKB = element.files[0].size / 1024;
                return fileSizeKB <= maxSizeKB;
            }, 'File size must be less than {0} KB.');

            $.validator.addMethod("pattern", function(value, element, param) {
                if (this.optional(element)) {
                    return true;
                }
                if (typeof param === "string") {
                    param = new RegExp(param);
                }
                return param.test(value);
            }, "Invalid format");

            // Edit staff click handler
            $('.edit-staff').click(function() {
                var staffId = $(this).data('id');
                $.ajax({
                    url: 'staffs/' + staffId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        // Populate form fields with edit- prefixed IDs
                        $('#edit-staff-id').val(staffId);
                        $('#edit-name').val(data.name);
                        $('#edit-email').val(data.email);
                        $('#edit-category_id').val(data.category_id).trigger('change');
                        $('#edit-phone').val(data.phone);
                        $('#edit-facebook').val(data.facebook);
                        $('#edit-instagram').val(data.instagram);
                        $('#edit-youtube').val(data.youtube);
                        $('#edit-tiktok').val(data.tiktok);

                        // Handle image display
                        if (data.image) {
                            var baseImageUrl = $('meta[name="asset-url"]').attr('content') ||
                                '/website';
                            var imageUrl = baseImageUrl + '/' + data.image;
                            var imageInput = $(
                                '#editStaffModal .image-input[data-kt-image-input="true"]');
                            var wrapper = imageInput.find('.image-input-wrapper');
                            wrapper.css('background-image', 'url(' + imageUrl + ')');
                            imageInput.removeClass('image-input-empty').addClass(
                                'image-input-changed');
                            imageInput.find('[data-kt-image-input-action="remove"]')
                                .removeClass('d-none');
                            imageInput.find('[data-kt-image-input-action="cancel"]')
                                .removeClass('d-none');
                        } else {
                            var imageInput = $(
                                '#editStaffModal .image-input[data-kt-image-input="true"]');
                            imageInput.addClass('image-input-empty').removeClass(
                                'image-input-changed');
                            imageInput.find('.image-input-wrapper').css('background-image',
                                'none');
                        }

                        // Load subcategories for the selected category
                        if (data.category_id) {
                            loadSubcategoriesForEdit(data.category_id, data.subcategory_id);
                        }

                        // Populate availability data if exists
                        if (data.availability_data && data.availability_data.length > 0) {
                            populateAvailabilityData(data.availability_data, data
                                .vacations_data || [], data.recurring_data || {});
                            $('#edit-availability-btn').html(
                                'Availability Set <span><i class="fa-solid fa-check text-success"></i></span>'
                                ).addClass('text-success');
                        } else {
                            // Reset availability button
                            $('#edit-availability-btn').html(
                                'Select availability<span><i class="fa-solid fa-chevron-down"></i></span>'
                                ).removeClass('text-success');
                            clearAvailabilityData();
                        }

                        $('#editStaffModal').modal('show');
                    },
                });
            });

            // Function to load subcategories for edit modal
            function loadSubcategoriesForEdit(categoryId, selectedSubcategoryId = null) {
                var subcategorySelect = $('#edit-subcategory_id');
                subcategorySelect.empty().append('<option></option>');
                subcategorySelect.prop('disabled', false);
                subcategorySelect.attr('data-placeholder', 'Loading subcategories...');

                $.ajax({
                    url: '/subcategories/get-subcategories/' + categoryId,
                    type: 'GET',
                    success: function(response) {
                        if (response.status && response.data && response.data.length > 0) {
                            $.each(response.data, function(index, subcategory) {
                                if (subcategory && subcategory.id && subcategory.name) {
                                    var selected = selectedSubcategoryId == subcategory.id ?
                                        'selected' : '';
                                    subcategorySelect.append('<option value="' + subcategory
                                        .id + '" ' + selected + '>' + subcategory.name +
                                        '</option>');
                                }
                            });
                            subcategorySelect.attr('data-placeholder', 'Select a subcategory');
                        } else {
                            subcategorySelect.attr('data-placeholder', 'No subcategories available');
                        }
                        subcategorySelect.select2({
                            dropdownCssClass: 'w-619px',
                            closeOnSelect: true,
                            allowClear: true
                        });
                    },
                    error: function() {
                        subcategorySelect.attr('data-placeholder', 'Error loading subcategories');
                        subcategorySelect.select2({
                            dropdownCssClass: 'w-619px',
                            closeOnSelect: true,
                            allowClear: true
                        });
                    }
                });
            }

            // Handle category change in edit modal
            $('#edit-category_id').on('change', function() {
                var categoryId = $(this).val();
                var subcategorySelect = $('#edit-subcategory_id');

                subcategorySelect.empty().append('<option></option>');

                if (categoryId) {
                    loadSubcategoriesForEdit(categoryId);
                } else {
                    subcategorySelect.prop('disabled', true);
                    subcategorySelect.attr('data-placeholder', 'Select a category first');
                    subcategorySelect.select2({
                        dropdownCssClass: 'w-619px',
                        closeOnSelect: true,
                        allowClear: true
                    });
                }
            });

            // Edit form validation and AJAX submission
            $("#editStaffForm").validate({
                errorClass: "error",
                errorElement: "span",
                errorPlacement: function(error, element) {
                    error.insertAfter(element);
                },
                rules: {
                    avatar: {
                        maxFileSize: 5120
                    },
                    name: {
                        required: true,
                        maxlength: 30,
                        pattern: /^[a-zA-Z0-9\s]+$/
                    },
                    email: {
                        required: true,
                        maxlength: 30,
                    },
                    category_id: {
                        required: true
                    },
                    subcategory_id: {
                        required: true
                    },
                    // facebook: {
                    //     required: true,
                    // },
                    // instagram: {
                    //     required: true,
                    // },
                    // tiktok: {
                    //     required: true,
                    // },
                    // youtube: {
                    //     required: true,
                    // },
                    phone: {
                        required: true,
                        pattern: /^[0-9+\-\s()]+$/
                    }
                },
                messages: {
                    avatar: {
                        maxFileSize: "Image size must not exceed 5 MB"
                    },
                    name: {
                        required: "Please enter staff name",
                        maxlength: "Staff name is too long try something shorter",
                        pattern: "Staff name can only contain letters, numbers, and spaces"
                    },
                    email: {
                        required: "Please enter email",
                        maxlength: "Email is too long",
                    },
                    category_id: {
                        required: "Please select category"
                    },
                    subcategory_id: {
                        required: "Please select sub category"
                    },
                    // facebook: {
                    //     required: "Please enter valid facebook url"
                    // },
                    // instagram: {
                    //     required: "Please enter valid instagram url"
                    // },
                    // tiktok: {
                    //     required: "Please enter valid tiktok url"
                    // },
                    // youtube: {
                    //     required: "Please enter valid youtube url"
                    // },
                    phone: {
                        required: "Please enter phone number",
                        pattern: "Please enter valid phone number"
                    }
                },
                submitHandler: function(form) {
                    var staffId = $('#edit-staff-id').val();
                    var formData = new FormData(form);
                    $.ajax({
                        url: '/staffs/' + staffId,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#editStaffModal').modal('hide');
                            Swal.fire({
                                icon: response.type,
                                title: response.title,
                                text: response.message
                            });
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        },
                        error: function(xhr) {
                            if (xhr.status === 422) {
                                var errors = xhr.responseJSON.errors;
                                $.each(errors, function(key, value) {
                                    var input = $('[name="' + key + '"]');
                                    input.addClass('is-invalid');
                                    input.after('<span class="error">' + value[0] +
                                        '</span>');
                                });
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: 'Update failed. Please try again.'
                                });
                            }
                        }
                    });
                }
            });
            // Reset edit modal validation when opened
            $('#editStaffModal').on('show.bs.modal', function() {
                if ($("#editStaffForm").data('validator')) {
                    $("#editStaffForm").validate().resetForm();
                    $('.is-invalid').removeClass('is-invalid');
                    $('.error').remove();
                }
            });
            // Function to populate availability data in the modal
            function populateAvailabilityData(availabilityData, vacationsData, recurringData) {
                // Store data in hidden inputs
                $('#edit-availability-data').val(JSON.stringify(availabilityData));
                $('#edit-vacations-data').val(JSON.stringify(vacationsData));
                $('#edit-recurring-data').val(JSON.stringify(recurringData));
                // Set global variables for the availability modal to use
                if (typeof window.setEditAvailabilityData === 'function') {
                    window.setEditAvailabilityData(availabilityData, vacationsData, recurringData);
                }
            }
            // Function to clear availability data
            function clearAvailabilityData() {
                $('#edit-availability-data').val('');
                $('#edit-vacations-data').val('');
                $('#edit-recurring-data').val('');
                // Clear global variables
                if (typeof window.clearEditAvailabilityData === 'function') {
                    window.clearEditAvailabilityData();
                }
            }
        });

        // Search and Filter functionality
        let searchTimeout;
        let currentStatus = 'all';

        // Handle search input with debounce
        $('#customSearchInput').on('keyup', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                filterStaffs();
            }, 300);
        });

        // Handle status dropdown selection
        $('.dropdown-status').on('click', function(e) {
            e.preventDefault();
            const label = $(this).data('label');
            const value = $(this).data('value');
            const color = $(this).data('color');

            // Update dropdown button
            $('.status-dropdown-button span').html(`<span class="dot" style="background-color: ${color};"></span> ${label}`);

            currentStatus = value;
            filterStaffs();
        });

        // Filter function
        function filterStaffs() {
            const search = $('#customSearchInput').val().trim();

            // Prepare data object - only include non-empty values
            let data = {};

            if (search) {
                data.search = search;
            }

            if (currentStatus && currentStatus !== 'all') {
                data.status = currentStatus;
            }

            // Show loading state
            showLoadingState();

            $.ajax({
                url: "{{ route('staffs.filter') }}",
                type: "GET",
                data: data,
                beforeSend: function() {
                    $('#customSearchInput').addClass('searching');
                },
                success: function(response) {
                    // Update table content
                    $('#staffTableBody').html(response.html);

                    // Update pagination
                    if (response.has_pages) {
                        $('#staffPagination').html(response.pagination).show();
                    } else {
                        $('#staffPagination').hide();
                    }

                    // Re-bind event handlers for new content
                    bindStaffToggleEvents();
                    bindEditStaffEvents();

                    hideLoadingState();
                },
                error: function(xhr, status, error) {
                    console.error('Filter error:', error);
                    hideLoadingState();

                    // Show error message
                    $('#staffTableBody').html(`
                        <tr>
                            <td colspan="5" class="text-center py-5">
                                <div class="d-flex flex-column align-items-center justify-content-center" style="min-height: 200px;">
                                    <div class="mb-3">
                                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem; opacity: 0.3;"></i>
                                    </div>
                                    <h5 class="text-danger mb-2">Error Loading Staff</h5>
                                    <p class="text-muted mb-0">Please try again later</p>
                                </div>
                            </td>
                        </tr>
                    `);
                },
                complete: function() {
                    $('#customSearchInput').removeClass('searching');
                }
            });
        }

        // Show loading state
        function showLoadingState() {
            $('#staffTableBody').html(`
                <tr>
                    <td colspan="5" class="text-center py-5">
                        <div class="d-flex flex-column align-items-center justify-content-center" style="min-height: 200px;">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="text-muted mb-0">Loading staff members...</p>
                        </div>
                    </td>
                </tr>
            `);
        }

        // Hide loading state
        function hideLoadingState() {
            // Loading state is replaced by actual content in success callback
        }

        // Re-bind staff toggle events after AJAX update
        function bindStaffToggleEvents() {
            $('.staff-toggle').off('change').on('change', function() {
                const staffId = $(this).data('staff-id');
                const status = $(this).is(':checked') ? 1 : 0;

                $.ajax({
                    url: '{{ route('staffs.update-status') }}',
                    method: 'POST',
                    data: {
                        staff_id: staffId,
                        status: status,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update toggle label
                            const label = status ? 'Active' : 'Deactive';
                            $(this).siblings('.toggle-label').text(label);
                        }
                    }.bind(this)
                });
            });
        }

        // Re-bind edit staff events after AJAX update
        function bindEditStaffEvents() {
            $('.edit-staff').off('click').on('click', function() {
                var staffId = $(this).data('id');
                $.ajax({
                    url: 'staffs/' + staffId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        // Same logic as existing edit staff handler
                        $('#edit-staff-id').val(staffId);
                        $('#edit-name').val(data.name);
                        $('#edit-email').val(data.email);
                        $('#edit-category_id').val(data.category_id).trigger('change');
                        $('#edit-phone').val(data.phone);
                        $('#edit-facebook').val(data.facebook);
                        $('#edit-instagram').val(data.instagram);
                        $('#edit-youtube').val(data.youtube);
                        $('#edit-tiktok').val(data.tiktok);

                        // Handle image display
                        if (data.image) {
                            var baseImageUrl = $('meta[name="asset-url"]').attr('content') || '/website';
                            var imageUrl = baseImageUrl + '/' + data.image;
                            var imageInput = $('#editStaffModal .image-input[data-kt-image-input="true"]');
                            var wrapper = imageInput.find('.image-input-wrapper');
                            wrapper.css('background-image', 'url(' + imageUrl + ')');
                            imageInput.removeClass('image-input-empty').addClass('image-input-changed');
                            imageInput.find('[data-kt-image-input-action="remove"]').removeClass('d-none');
                            imageInput.find('[data-kt-image-input-action="cancel"]').removeClass('d-none');
                        } else {
                            var imageInput = $('#editStaffModal .image-input[data-kt-image-input="true"]');
                            imageInput.addClass('image-input-empty').removeClass('image-input-changed');
                            imageInput.find('.image-input-wrapper').css('background-image', 'none');
                        }

                        // Load subcategories for the selected category
                        if (data.category_id) {
                            loadSubcategoriesForEdit(data.category_id, data.subcategory_id);
                        }

                        // Populate availability data if exists
                        if (data.availability_data && data.availability_data.length > 0) {
                            populateAvailabilityData(data.availability_data, data.vacations_data || [], data.recurring_data || {});
                            $('#edit-availability-btn').html('Availability Set <span><i class="fa-solid fa-check text-success"></i></span>').addClass('text-success');
                        } else {
                            $('#edit-availability-btn').html('Select availability<span><i class="fa-solid fa-chevron-down"></i></span>').removeClass('text-success');
                            clearAvailabilityData();
                        }

                        $('#editStaffModal').modal('show');
                    },
                });
            });
        }

        // Initial binding
        bindStaffToggleEvents();
        bindEditStaffEvents();
    </script>
@endpush

/* Sora */
@import url('https://fonts.googleapis.com/css2?family=Hanken+Grotesk:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Manrope:wght@200..800&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100..900;1,100..900&family=Rubik:ital,wght@0,300..900;1,300..900&family=Sora:wght@100..800&display=swap');
/* Inter */
@import url('https://fonts.googleapis.com/css2?family=Hanken+Grotesk:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Manrope:wght@200..800&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100..900;1,100..900&family=Rubik:ital,wght@0,300..900;1,300..900&family=Sora:wght@100..800&display=swap');
/* Nunito Sans */
@import url('https://fonts.googleapis.com/css2?family=Hanken+Grotesk:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Manrope:wght@200..800&family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100..900;1,100..900&family=Rubik:ital,wght@0,300..900;1,300..900&family=Sora:wght@100..800&display=swap');
/*  Outfit */
@import url('https://fonts.googleapis.com/css2?family=Hanken+Grotesk:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Manrope:wght@200..800&family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Outfit:wght@100..900&family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Raleway:ital,wght@0,100..900;1,100..900&family=Rubik:ital,wght@0,300..900;1,300..900&family=Sora:wght@100..800&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

:root {
    --white: #fff;
    --black: #000;
    --neutral-gray: #F0F0F0;
    --whisper-gray: #F7F7F7;
    --deep-blue: #020C87;
    --semi-transparent-black: rgba(0, 0, 0, 0.5);
    --gray: #A5A5A5;
    --ocean-blue: #065FAA;
    --steel-blue: #4682B4;
    --slate-gray: #708090;
    --snow-white: #FAFAFA;
    --gray: rgba(255, 255, 255, 0.30); ;
    --light-gray: #6E7A84;
    --dark-blue: #1E3A5F;
    --light-black: #363636;
    --ice-blue: #F4F9FF;
    --cultured-white: #F9F9F9;
    --red: #EA4335;
    --dull-gray: rgba(54, 54, 54, 0.60);
    --lightest-black: #363636;
    --link-gray: #9A9EA6;
    --navy-blue: #172633;
    --border-color: #E5E7EB;
    --yellow: #FCAA00;
    --input-border:#DCDDE8;
}

/* Scrollbar */
::-webkit-scrollbar { width: 10px; background-color: transparent;}
::-webkit-scrollbar-track {  background-color: transparent;}
::-webkit-scrollbar-thumb {  background-color: #888; border-radius: 20px;}
::-webkit-scrollbar-thumb:hover {  background-color: #555;}

.scrolltop { background: var(--whisper-gray); border: 1px solid transparent;}
.scrolltop:hover {  border: 1px solid var(--ocean-blue);  background: var(--white);}
.scrolltop:hover i { color: var(--ocean-blue);}

/* Font Sizes */
h1, h2, h3, h4, h5, h6 { line-height: 1.2;  word-break: break-word; }
p, a, span { line-height: 1.5; word-break: break-word;}
li {    list-style: none;}

h1 { font-size: 48px;  font-weight: 800;}
h2 { font-size: 40px; font-weight: 800; } 
h3 { font-size: 39px; font-weight: 600; } 
h4 { font-size: 34px; font-weight: 600; } 
h5 { font-size: 27px; font-weight: 700; } 
h6 { font-size: 22px; font-weight: 500; }

.fs-34{font-size: 34px;}
p, .fs-18 { font-size: 18px; font-weight: 400; }

/* Font Weight */
.extra-bold{font-weight: 800;}
.bold { font-weight: 700; } 
.semi_bold { font-weight: 600; } 
.regular { font-weight: 500; } 
.normal { font-weight: 400; } 

.fs-20 { font-size: 20px; } 
.fs-16 { font-size: 16px; } 
.fs-15 { font-size: 15px; } 
.fs-14 { font-size: 14px; } 
.fs-13 { font-size: 13px; } 
.fs-12 { font-size: 12px; } 
.fs-11 { font-size: 11px; } 
.fs-10px { font-size: 10px; }

/* Font Family */
.inter { font-family: 'Inter', sans-serif }
.nunito-sans { font-family: 'Nunito Sans', sans-serif; } 
.sora { font-family: 'Sora', sans-serif; } 
.outfit { font-family: 'Outfit', sans-serif; }

/* Buttons Styling */
.button { color: var(--white); background-color: var(--deep-blue); border-radius: 30px; border: 1px solid transparent; text-align: center; font-family: 'Sora', sans-serif;font-size: 14px; font-weight: 600;padding: 10px 38px; }
.button:hover,.blue-button:hover {background: transparent;color: var(--deep-blue);border: 1px solid var(--deep-blue);}
.button:hover i { color: var(--deep-blue);}
.blue-button {padding: 10px 62px;border-radius: 6px;color: var(--snow-white);background: var(--deep-blue);box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.10), 0px 1px 2px -1px rgba(0, 0, 0, 0.10);font-family: 'Inter', sans-serif;font-size: 13px;font-weight: 500;align-content: center;border: 1px solid transparent;}
/* .trans-button {background: transparent;border: 1px solid var(--gray);} */
.trans-button{background: transparent; border: 1px solid darkgray; padding: 14px 12px; border-radius: 8px; }
.partner.business_partner .button.trans-button:hover {color: var(--white);border: 1px solid var(--white);}
.add-btn{border: 0;padding: 12px 18px; font-family: Inter;color: var(--white); font-size: 16px; font-style: normal; font-weight: 600; border-radius: 8px; background: var(--deep-blue);}
.professional-btn { background-color: var(--ocean-blue);}
.professional-btn:hover {border: 1px solid var(--white);color: var(--white);}
.partner.business_partner .button.professional-btn:hover i{color: var(--white);}

/* badge */
.white-badge { border-radius: 100px; border: 1px solid var(--neutral-gray); background: var(--white); color: var(--light-black); font-family: Inter; font-size: 13px; font-weight: 500; padding: 8px 12px; }

/* colors */
.white { color: var(--white); } 
.black { color: var(--black); } 
.light-gray { color: var(--light-gray); } 
.steel-blue { color: var(--steel-blue); } 
.dark-blue { color: var(--dark-blue); } 
.light-black { color: var(--light-black); } 
.dull-gray { color: var(--dull-gray); } 
.lightest-black { color: var(--lightest-black); }
.link-gray { color: var(--light-gray); } 
.deep-blue { color: var(--deep-blue); } 
i.icon-color { color: var(--dark-blue); } 
.padding-block { padding-block: 5em; } 
.padding-inline { padding-inline: 6em; } 
.padding { padding-block: 3em; } 
.opacity { opacity: 0.5; } 
.opacity-6 { opacity: 0.6; } 
.border-right { border-right: 1px solid var(--border-color); }
.letter-space { letter-spacing: 3px; } 

input { border: none; background: transparent; } 
input:focus { border: none; box-shadow: none; outline: 0 } 

/*Header*/ .header .header-search { border-radius: 30px; background: #EBF2F8; } 
.discount-header { background: var(--navy-blue); } .header-items { box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.06); }
.header-items .nav-item a.nav-link { color: var(--light-gray); position: relative; border-bottom: 1px solid transparent; transition: color 0.6s ease; }
.header-items .nav-item a.nav-link.blue-text{color: var(--deep-blue);}

/*.header-items .nav-item a.nav-link:last-child{color: var(--deep-blue);}*/
/* .header-items .nav-item:last-child .nav-link {  color: var(--deep-blue);} */

.discount-header i{color: var(--white);} 
.header-items .nav-item a.dropdown-toggle { color: var(--light-gray); } 
.header-items .nav-item:has(.nav-link.active.header-active ) a.dropdown-toggle { color: var(--white); } 
.header-items .nav-item .nav-link.active.header-active  { border-radius: 6px; background: var(--deep-blue); color: var(--white); } 
.header-items .nav-item:has(.nav-link.active.header-active) { border-radius: 6px; background: var(--deep-blue); color: var(--white); }
.header-items .nav-item a.nav-link::before { content: ''; position: absolute; left: 0; bottom: -1px; width: 100%; height: 1px; background-color: var(--deep-blue); transform: scaleX(0); transform-origin: left; transition: transform 0.6s ease; } 
.header-items .nav-item a.nav-link:hover::before { transform: scaleX(1); } .header-items .nav-item a.nav-link:hover { color: var(--deep-blue); } 
.header-items .nav-item .nav-link.active.header-active:hover { color: var(--white); } 
.header-items .nav-item:has(.nav-link):hover a.dropdown-toggle { color: var(--deep-blue); } 
.header-items .nav-item:has(.nav-link.active.header-active):hover a.dropdown-toggle { color: var(--white); }
.custom-select-location select, .custom-select-location select:focus-visible { border: none; outline: unset; font-size: 14px; } 

.navbar-header i { font-size: 20px; color: #9A9EA6 !important; } 
.app-navbar-item.user-info { border-radius: 20px; border: 1px solid var(--Light-Grey-2, #E5E7EB); background: #FFF; padding: 5px 8px; } 
.app-navbar-item.user-info i, .app-navbar-item.user-info span { color: var(--deep-blue); font-size: 12px; } 
.navbar-header .symbol>img { width: 35px; height: 35px; border-radius: 50%; }
img.user-profile-pctr { border-radius: 50%; } 
a.see-all-btn { border-radius: 6px; background: #F7F7F7; border: 1px solid #F7F7F7; padding: 10px; text-align: center; margin-top: 2em; color: #020C87; text-decoration: underline; font-weight: 700; }

/* Footer */
.footer { background: var(--whisper-gray); } 
.footer .footer-border-bottom { border-bottom: 1px solid var(--border-color); } 
.footer .input-box { border-radius: 30px; padding: 9px 8px 9px 24px; background: var(--white); } 
.footer .input-box input:focus { outline: 0; box-shadow: none; } .footer .input-box input { border: none; } 
.footer .input-box input[type="submit"] { border-radius: 30px; background: var(--deep-blue); color: var(--white); padding: 10px 26px; font-family: "Nunito Sans"; font-size: 12px; font-weight: 700; } 
.footer .input-box input::placeholder { color: #9A9EA6; font-family: Inter; font-size: 14px; font-weight: 500; }

/* Home page */
.gradient_heading { background: linear-gradient(to right,rgba(252, 170, 0, 1), rgba(26, 188, 254, 1) ); -webkit-background-clip: text; -webkit-text-fill-color: white; -webkit-text-stroke: 4px transparent; }
.home-banner .para-width {  width: 690px;}
.home-banner { /* height: 100vh; */ height:  calc(100vh - 185px); background: linear-gradient(0deg, rgba(135, 206, 234, 0.30) 0%, rgba(135, 206, 234, 0.30) 100%), url(../images/home-banner.png); background-blend-mode: color; background-position: center; background-repeat: no-repeat; background-size: cover; overflow: hidden; position: relative; /* padding-block: 14em; */ }
/*.home-banner .banner-bg::after{ content: '';*/ /*    !*height: 745px;*!*/ /*    height: 100%;*/ /*    width: 48%;*/ /*    background-image: url(../images/blue-circle.png);*/ /*    background-size: contain;*/ /*    background-position: center;*/ /*    background-repeat: no-repeat;*/ /*    position: absolute;*/ /*    right: 0;*/ /*    margin: auto;*/ /*    top: 69px;*/ /*    bottom: auto;}*/
.home-banner .white-box { border-radius: 16px; background: var(--white); padding: 18px; z-index: 9999; box-shadow: 0px 16px 8px 0px rgba(0, 0, 0, 0.02); } 
.home-banner .home-card-image { height: 100%; width: 45%; object-fit: contain; right: 0; bottom: auto; z-index: 99; }
/* .home-banner .image-box::after { position: absolute; content: ''; height: 100%; width: 70%; background-size: contain; background-position: center; background-repeat: no-repeat; position: absolute; right: -225px; margin: auto; top: 104px; bottom: auto; z-index: 6; } */
/*.home-banner  .image-box::before{*/ /*    position: absolute; content: '';*/ /*    height: 100%;*/ /*    width: 237px;*/ /*    background-image: url(../images/calender.png);*/ /*    background-size: contain;*/ /*    background-position: center;*/ /*    background-repeat: no-repeat;*/ /*    position: absolute;*/ /*    right: 22px;*/ /*    margin: auto;*/ /*    top: -79px;*/ /*    bottom: auto;*/ /*}*/

.home-banner .home-content { z-index: 9999; } 
.home-banner .white-box .form-control input { background: transparent; border: none; } 
.home-banner .white-box .form-control input:focus { outline: 0; box-shadow: none; }
.home-banner .form-control.home-input{border-radius: 10px;border: 1px solid var(--input-border); background: var(--white);    padding: 14px 12px 14px 16px;}
.home-banner .form-control.home-input i{color: rgba(112, 128, 144, 1);}
.home-banner .white-box .blue-button{padding: 14px 62px;}

.analysis {  border-bottom: 1px solid var(--neutral-gray);}
.analysis .gray-boxselect2-selection { border-radius: 100px; border: 1px solid #EBF2F4; background: linear-gradient(0deg, rgba(6, 95, 170, 0.08) 0%, rgba(6, 95, 170, 0.08) 100%), var(--white); padding: 11px 34px; } 
.top-rated .container { border-radius: 8px; padding: 30px; background: var(--ice-blue); } 
body .consumers { background-image: radial-gradient(65% 60% at 50% 50%, rgba(255, 255, 255, 0.30) 0%, #FFF 80%), url(../images/bg-image.png); /* background-image: radial-gradient(70.71% 70.71% at 50% 50%, rgba(255, 255, 255, 0.30) 0%, #FFF 80%),url(../images/bg-image.png); */ background-size: cover; background-position: center; background-repeat: no-repeat; } 

.top-rated-card { border-radius: 8px; border: 1px solid var(--neutral-gray); background: var(--cultured-white); }
.top-rated-card .card-header .top-rated-image { border-radius: 10px 10px 0 0; object-fit: cover; width: 100%; height: 250px !important;} 
.top-rated-card .card-header .top-rated-image img {object-fit: cover;}

.top-rated-card .card-header .fav-icon { border-radius: 100px; border: 1px solid var(--neutral-gray); background: var(--white); height: 35px; width: 35px; padding: 8px; text-align: center; bottom: 7px; right: 7px;display: flex;justify-content: center;align-items: center; }
.top-rated-card .card-header .fav-icon i { color: var(--red); font-size: 18px;}

.rated-swipper,.category-swipper {position: unset;} .rated-swipper .swiper-button-next {right: -55px;} 
.rated-swipper .swiper-button-prev{left: -55px;}

.pagination .top-rated-swiper { height: 485px;}
/* .pagination .top-rated-swiper { height: 380px;} */
.pagination .top-rated-swiper .swiper-pagination.swiper-pagination-bullets.swiper-pagination-horizontal { top: 37em;}

.top-rated-services-swiper .swiper-button-next ,.top-rated-services-swiper .swiper-button-prev, .rated-swipper .swiper-button-next, .rated-swipper .swiper-button-prev,
 .category-swipper .swiper-button-next, .category-swipper .swiper-button-prev, .nearby-professionals .swiper-button-prev, .nearby-professionals .swiper-button-next
  { border-radius: 153.846px; background: var(--white); box-shadow: 0px 2.308px 9.231px 0px rgba(0, 0, 0, 0.10); HEIGHT: 40px; width: 40px; }
.top-rated-services-swiper .swiper-button-next::after,.top-rated-services-swiper .swiper-button-prev::after,  .rated-swipper .swiper-button-next::after, .rated-swipper .swiper-button-prev::after, .category-swipper .swiper-button-prev::after, .category-swipper .swiper-button-next::after { font-size: 15px; color: var(--black); }
.top-rated-services-swiper .swiper-button-next.swiper-button-disabled, .rated-swipper .swiper-button-prev.swiper-button-disabled, .rated-swipper .swiper-button-next.swiper-button-disabled, .rated-swipper .swiper-button-prev.swiper-button-disabled, .category-swipper .swiper-button-next.swiper-button-disabled, .category-swipper .swiper-button-prev.swiper-button-disabled { opacity: 0; }
.category-swipper .swiper-button-next { right: -20px; top: 12em;} 
.category-swipper .swiper-button-prev { left: -20px; top: 12em;} 
.category-swipper .professional-card { background: var(--ice-blue); } 
.pagination .top-rated-swiper .swiper-pagination-bullet, .rated-swipper span.swiper-pagination-bullet { height: 10px; width: 10px; border-radius: 10px; border: 1px solid var(--dark-blue); background: transparent; }
.pagination .top-rated-swiper .swiper-pagination-bullet, .rated-swipper span.swiper-pagination-bullet.swiper-pagination-bullet-active {  background: var(--dark-blue);}
.rated-swipper .swiper-pagination.swiper-pagination-bullets.swiper-pagination-horizontal {  bottom: -53px;}

.nearby-professionals .swiper-button-next {
    right: -15px;
    top: 14em;
}

.nearby-professionals .swiper-button-prev {
    left: -15px;
    top: 14em;
}

/* .top-rated .container {
    border-radius: 8px;
    padding-inline: 30px;
    background: var(--ice-blue);
    padding-top: 3em;
    padding-bottom: 8em;
} */


/* top-rated-swiper */

/* .top-rated-swiper .swiper-button-prev {left: -20px;}
.top-rated-swiper .swiper-button-next {right: -20px;} */

.top-rated-swiper .swiper-button-next, .top-rated-swiper .swiper-button-prev {   border-radius: 153.846px;   background: var(--white);  box-shadow: 0px 2.308px 9.231px 0px rgba(0, 0, 0, 0.10);  height: 40px;  width: 40px; color: #000;     top: 8em;}
.top-rated-swiper  .swiper-button-prev:after, .top-rated-swiper .swiper-button-next:after { font-size: 14px; font-weight: bolder;}
.top-rated-swiper  .swiper-button-prev:after { content: 'prev';}
.top-rated-swiper .swiper-button-next:after{  content: 'next';}

.consumers .blue-icon { border-radius: 7px; padding: 21px; background: var(--steel-blue); } 
.partner { height: 380px; background: linear-gradient(244deg, rgba(9, 41, 72, 0.30) -4.77%, rgba(9, 41, 72, 0.30) 49.35%, rgba(9, 41, 72, 0.93) 87.97%), url(../images/partner-bg.png); background-size: cover; background-position: center; background-repeat: no-repeat; }
.rated-div { border-radius: 20px; border: 1px solid var(--yellow); background: var(--white); padding: 5px 8px; right: 8px; top: 10px; }

/* Service page */
.service .service-tab { padding: 7px 12px; min-width: 208px; border-radius: 20px; width: fit-content; background: var(--cultured-white); color: #363636; border: 1px solid transparent; opacity: 0.6; font-family: Sora; font-size: 14px; font-weight: 400; } 
.service .service-tab.active { border: 1px solid var(--deep-blue); background: var(--white); color: var(--deep-blue); opacity: unset; font-weight: 600; } 
.service .professional-tab { border-radius: 6px; padding: 7px 12px; background: var(--cultured-white); color: #363636; font-family: Sora; font-size: 14px; font-weight: 400; opacity: 0.6; min-width: 208px; }
.service .professional-tab.active { background: #020C87; opacity: unset; } 
.service-swipper, .professional-swipper { position: unset; } 

.search-bar { border-radius: 6px; border: 1px solid var(--border-color); background: var(--white); padding: 5px 8px 5px 16px; width: 314px; font-size: 14px; }
.search-bar input { border: none; background: transparent } 
.search-bar input:focus { outline: 0; box-shadow: none; } 
.service-swipper .swiper-button-next, .service-swipper .swiper-button-prev, .professional-swipper .swiper-button-next, .professional-swipper .swiper-button-prev { border-radius: 4px; background: var(--cultured-white); padding: 4px; height: 24px; width: 24px; }
.services-card .card-footer { background-color: var(--ice-blue); } 
.service-swipper .swiper-button-next:after, .service-swipper .swiper-button-prev:after, .professional-swipper .swiper-button-next:after, .professional-swipper .swiper-button-prev:after { color: black; font-size: 14px; font-weight: 900; } 
.service-swipper .swiper-button-prev, .professional-swipper .swiper-button-prev { right: 44PX; left: auto; }
.service-swipper .swiper-button-prev, .service-swipper .swiper-button-next, .professional-swipper .swiper-button-prev, .professional-swipper .swiper-button-next { top: 62px; }

.professional-card img.top-rated-image { width: 100%; height: 250px; }

.filter-select { border-radius: 20px; border: none; background: var(--cultured-white); padding: 7px 12px; text-align: center; } 
.filter-select select { appearance: none; border: none; background: transparent; color: #000; font-family: Sora; font-size: 14px; font-weight: 400; } 
.filter-select select:focus { border: none; box-shadow: none; outline: 0; }
.select2-container--default .select2-selection--single { background: transparent; border: none; font-family: Sora; font-size: 14px; font-weight: 400; color: #000; padding: 0; height: auto; } 
.select2-container--default .select2-selection--single .select2-selection__rendered { padding-left: 0; color: #000; } 
.select2-container--default .select2-selection--single .select2-selection__arrow { display: none; /* Hide default arrow if using your own icon */ }

/*privacy*/
.table-content{padding: 10px;}
.table-content.active {border-radius: 6px; background: var(--cultured-white); color: var(--deep-blue);font-weight: 600;}

/*loader*/
.logo-loader .text {font-size: 45px;font-weight: bold;color: var(--black);font-family: 'sora';}
.logo-loader { display: flex; justify-content: center; align-items: center; background-color: var(--whisper-gray); position: fixed; top: 0; left: 0; height: 100vh; width: 100vw; z-index: 9999999; }
.logo-loader .logo-container {  display: flex;  align-items: center;  justify-content: center; }
.logo-loader .circle {  width: 100px;  height: 80px;  animation: spin 2s linear infinite;  margin: 0;}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% {  transform: rotate(360deg); }
}

.family-cards { border-radius: 6.76px;border: 1.127px solid var(--neutral-gray);background: var(--cultured-white);box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05); padding: 32px 1px;}
.form-control.form-input {border-radius: 10px;border: 1px solid var(--input-border);padding: 14px 16px;background: var(--white);appearance: auto;}
.form-check-input:checked[type=radio] {  appearance: initial;background-color: var(--deep-blue);}
.form-check-input {width: 1rem;height: 1rem;}
.form-check-input[type=radio] {border-radius: 50%;}
.form-check-input[type=radio] { border-color: var(--deep-blue);}
.form-label.form-input-labels{color: var(--black);font-family: Sora;font-size: 13px;font-weight: 600;}

/* filter modal checkbox */
.category-checkbox {position: relative; display: inline-block;padding: 0; margin: 0; cursor: pointer;}
.category-checkbox input[type="checkbox"] { display: none;}
.category-checkbox span { display: inline-block; padding: 7px 12px; border: 1px solid transparent; transition: all 0.3s ease;border-radius: 20px; color: var(--light-black); font-family: Sora;font-size: 12px;font-weight: 400;;background: var(--whisper-gray);}
.category-checkbox input[type="checkbox"]:checked+span { color: var(--deep-blue); font-weight: 600; border: 1px solid var(--deep-blue);  background: var(--white);}
.filter-modal .modal-footer .add-btn {font-size: 13px;font-weight: 500;padding: 10px 16px;color: var(--snow-white);}
.filter-modal .modal-footer .trans-button {font-size: 13px;font-weight: 500;padding: 10px 16px;border-radius: 6px;border: 1px solid rgba(0, 0, 0, 0.10);  color: var(--black);}
/* Range Slider Styling */

/* Base slider style */
.styled-range { -webkit-appearance: none; width: 100%; height: 12px; background: linear-gradient(to right, #00008B 30%, #F0F0F0 30%); border-radius: 50px; outline: none; transition: background 450ms ease-in; }

/* Webkit Thumb */
.styled-range::-webkit-slider-thumb { -webkit-appearance: none; width: 28px; height: 28px; border-radius: 50%; background: #00008B; border: 5px solid #fff; box-shadow: 0 0 8px rgba(0, 0, 0, 0.15); cursor: pointer; margin-top: -8px; }

/* Firefox Thumb */
.styled-range::-moz-range-thumb { width: 28px; height: 28px; border-radius: 50%; background: #00008B; border: 5px solid #fff; cursor: pointer; box-shadow: 0 0 8px rgba(0, 0, 0, 0.15); }

/* Remove outline on focus */
.styled-range:focus { outline: none; } #priceValue { font-weight: bold; font-size: 16px; color: #1E3A8A; /* Blue color for the text */ } 

/*mega menu css*/ 
.mega_menu .dropdown-menu a { text-decoration: none; color: #000000; }
.mega_menu .dropdown-menu a .d-flex {  transition: all 0.5s;}
.mega_menu .dropdown-menu .col-sm-6:nth-child(1) a:hover .d-flex { background-color: var(--bs-warning-bg-subtle);}
.mega_menu .dropdown-menu .col-sm-6:nth-child(2) a:hover .d-flex {background-color: var(--bs-danger-bg-subtle);}
.mega_menu .dropdown-menu {background-color: var(--white);border-top: 1px solid var(--border-color);z-index: 9999999;}
.mega_menu .mega_menu_tabs{    color: rgba(54, 54, 54, 1);font-family: Sora;font-size: 16px;font-weight: 400;text-align: start;}
.mega_menu .mega_menu_tabs.active{border-radius: 4px; background: #F3F4F6;color: rgba(0, 0, 0, 1);}
.mega_menu .tab-content .tab-pane .category-box{border-radius: 4px;padding: 10px 16px;border: 1px solid var( --border-color);color: var(--light-black);font-family: Inter;font-size: 14px;font-weight: 400;opacity: 0.6;  cursor: pointer;  transition: 0.2s ease-in-out;}
.mega_menu .tab-content .tab-pane .category-box .arrow-icon{display: none;color: var(--black);font-size: 15px;font-weight: 600;}
.mega_menu .tab-content .tab-pane .category-box:hover{color: var(--black);box-shadow: 0px 10px 15px -3px #F3F4F6, 0px 4px 6px -4px #F3F4F6;opacity: 1;}
.mega_menu .tab-content .tab-pane .category-box:hover .arrow-icon{display: block;}

/* range-slider */
.slider-container .slider { -webkit-appearance: none; width: 100%;height: 12px;background:#F0F0F0 ;border-radius: 10px;outline: none;transition: background 0.3s ease;}
.slider-container .slider:focus {outline: none; }
.slider-container .slider::-webkit-slider-thumb { -webkit-appearance: none; appearance: none; width: 20px;height: 20px;border-radius: 50%; background: var(--deep-blue); /* White thumb */border: 4px solid white;/* White border around the thumb */ cursor: pointer; box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1); /* Raised effect with shadow */transition: background 0.3s ease, border 0.3s ease, box-shadow 0.3s ease;}

/* Change color of the thumb on hover */
.slider-container .slider::-webkit-slider-thumb:hover {background: var(--deep-blue); /* Slightly darker color on hover */ }

/* Firefox specific styles */
.slider-container .slider::-moz-range-thumb {width: 20px;height: 20px;border-radius: 50%;background: var(--deep-blue); /* White thumb */  border: 4px solid white; /* White border around the thumb */cursor: pointer; transition: background 0.3s ease, border 0.3s ease; }

/* Change color of the thumb on hover for Firefox */
.slider-container .slider::-moz-range-thumb:hover {background: #ccc; /* Slightly darker color on hover */ }

.right-sidebar-menus.menu-gray-800 .menu-item .menu-link.logout{color:red;}
.right-sidebar-menus.menu-state-color .menu-item.hover:not(.here)>.menu-link:not(.disabled):not(.active):not(.here), .right-sidebar-menus.menu-state-color .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here){color:var(--deep-blue);}
.service-subcategory{gap:41px;}
.partner .button.professional-btn i{color: var(--white);}
.partner .button.professional-btn:hover i{color: var(--ocean-blue);}
.partner .button.trans-button:hover{color: var(--ocean-blue); border: 1px solid var(--ocean-blue);}
.notification-dropdown{border-radius: 8px;border: 0.5px solid var(--border-color);background:var(--white);box-shadow: 0px 0px 22px 0px rgba(0, 0, 0, 0.08);position: relative;z-index: 99999 !important;}
.terms-condition :is(h1, h2, h3 , h4 , h5 , h6){font-size: 16px; color: var(--black);font-weight: 500;}
.terms-condition :is(p){font-size: 14px; color: var(--light-black);font-weight: 400; opacity: 0.6;}
.top-rated-card .card-header .fav-icon {cursor: pointer;}

.friends-cards  img.customer_profile { height: 200px; width: 200px; border-radius: 50%; }
.fixed-theme-header {  position: sticky;  top: 0;  z-index: 999999999;}
.top-rated-card .card-body .review-icon { color: var(--black);}
input[type="search"]::-webkit-search-cancel-button {cursor: pointer;}
.newsletter-footer .email-input svg{ height: 26px;  }
.modal {  z-index: 99999999999;}
.select2-container {  z-index: 9999;}
span.select2-selection.select2-selection--multiple:focus-visible { border: 1px solid #dbdfe9; outline: none; }
.select2-container{width: 100% !important;}

/* Sweetalert */
div:where(.swal2-container).swal2-top, div:where(.swal2-container).swal2-center, div:where(.swal2-container).swal2-bottom { z-index: 9999999; }
div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm { background-color: var(--deep-blue);}
body div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover { background-color: var(--white); color: var(--deep-blue); border: 1px solid var(--deep-blue); }

.social-block { border: 1px solid lightgray;  padding: 2em;  border-radius: 10px;  margin-block: 2em;}
.date-item { border-radius: 6px; border: 1px solid #F0F0F0; background: #FFF; box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05); padding: 12px 24px; } 
div#dateGrid { display: flex ; gap: 10px; width: 100%; } 
div#timeGrid { display: flex ; flex-wrap: wrap; gap: 10px; } 
.date-item.selected, .time-item.selected { border-radius: 6px; border:2px solid #020C87 !important; background: #FFF; } 
.time-item, div#selectionDisplay { border-radius: 6px; border-radius: 6px; border: 1px solid #F0F0F0; background: #FFF; box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05); padding: 8px 3em; } 
select#monthSelect { border: unset; margin-block: 1em; font-size: 14px; font-style: normal; font-weight: 600; }

.service-details-modal .service-details { border-radius: 8px; border: 1.127px solid #F0F0F0; background: #FFF; }

.category-checkbox:hover { border-color: #d1d5db;}
.category-checkbox input[type="radio"] {appearance: none; }
.category-checkbox{border-radius: 6px;border: 1px solid #F0F0F0; background: #FFF; box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05); padding: 12px; width: 100%;}
.category-checkbox .span-class-service{font-size: 13px; color: #36363699; background-color: #FFF;}
.category-checkbox p { display: flex; flex-direction: column; margin: 0; font-size: 16px; font-weight: 600; color: #1f2937; } 
.category-checkbox:has(input[type="radio"]:checked) { border-radius: 6px; border:2px solid #020C87; background: #FFF; box-shadow: 0px 1.127px 2.253px 0px rgba(0, 0, 0, 0.05); width: 100%; padding: 12px; }
/* .category-checkbox input[type="radio"]:checked+p */

.top-rated-services-swiper .swiper-button-next { right: -21px; top: 13em; } 
.top-rated-services-swiper .swiper-button-prev { left: -21px; top: 13em; }

.blue-icon img { height: 77px;  width: 77px;}

a{color: var(--deep-blue);}

.add-services-modal.service-details-modal .date-header {  display: flex; justify-content: space-between;}

.add-services-modal.service-details-modal .nav-buttons .nav-btn { border: 1px solid #80808030;  padding: 0px 6px;   border-radius: 5px;}

.toast-message {display: none;  position: fixed;   bottom: 30px;   right: 30px;  background-color: #28a745; color: #fff; padding: 12px 20px; border-radius: 5px;  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.2);  z-index: 9999;  font-size: 16px;}

figure.image.image-style-side img {  height: 100%; width: 100%;   object-fit: cover;}

a.learn-more-btn { background: transparent; border-radius: 30px; border: 1px solid rgba(255, 255, 255, 0.30); padding: 12px 24px; font-size: 14px; font-weight: 600; color: #FFF; }

.mySwiper.near_by_swiper{position: unset;}

/* Terms & Constions view */

.terms-condition [id^="section-"]::before {
  content: "";
  display: block;
  height: 200px;      /* header height */
  margin-top: -200px; /* negative pulls content back up */
  pointer-events: none;
}
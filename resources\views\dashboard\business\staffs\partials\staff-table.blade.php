@forelse ($staffs as $staff)
    <tr>
        <td data-label="">
            <div class="card  flex-row shadow-none p-0 gap-3 align-items-center">
                <div class="card-header p-0 border-0 align-items-start">
                    <img src="{{ asset('website') . '/' . $staff->image }}"
                        class="h-80px w-80px rounded-3 object-fit-contain"
                        alt="card-image" />
                </div>
                <div class="card-body p-0 ">
                    <p class="fs-16 regular black m-0">{{ $staff->name ?? '' }}</p>
                </div>
            </div>

        </td>
        <td data-label="Email Address">{{ $staff->email ?? '' }}</td>
        <td data-label="Category">{{ $staff->category->name ?? '' }}</td>
        {{-- <td data-label="Bookings  Per month">20% </td> --}}
        <td data-label="">
            <div class="toggle-container">
                <label class="switch">
                    <!-- Dynamically set checked based on category status -->
                    <input type="checkbox" class="toggle-input staff-toggle"
                        data-staff-id="{{ $staff->id }}"
                        {{ $staff->status == 1 ? 'checked' : '' }}>
                    <span class="slider"></span>
                </label>
                <span
                    class="toggle-label">{{ $staff->status == 1 ? 'Active' : 'Deactive' }}</span>
            </div>
        </td>
        <td data-label="">
            <div class="dropdown">
                <button class="drop-btn" type="button" id="dropdownMenuButton"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <li>
                        <a href="{{ route('staffs.show', $staff->ids) }}"
                            class="dropdown-item complete fs-14 regular " type="button">
                            <i class="fa-solid fa-eye complete-icon"></i>
                            View
                        </a>
                    </li>
                    <li>
                        <button class="dropdown-item deep-blue fs-14 regular edit-staff"
                            type="button" data-bs-toggle="modal"
                            data-bs-target="#editStaffModal" data-id="{{ $staff->ids }}">
                            <i class="fa-solid fa-pen-to-square"></i>
                            Edit
                        </button>
                    </li>
                </ul>
            </div>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="5" class="text-center py-5">
            <div class="d-flex flex-column align-items-center justify-content-center" style="min-height: 200px;">
                <div class="mb-3">
                    <i class="fas fa-users text-muted" style="font-size: 3rem; opacity: 0.3;"></i>
                </div>
                <h5 class="text-muted mb-2">No Staff Members Found</h5>
                <p class="text-muted mb-0">Try adjusting your search or filter criteria</p>
            </div>
        </td>
    </tr>
@endforelse

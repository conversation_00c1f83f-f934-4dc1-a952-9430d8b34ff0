/* Chat Container Styles */
.chat-container {
    height: calc(100vh - 80px);
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    padding: 20px;
}

/* Sidebar Styles */
.chat-sidebar {
    background: #fff;
    border-right: 1px solid #e9ecef;
    height: 100%;
    overflow-y: auto;
    width: 320px;
    min-width: 320px;
}

.chat-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: #fff;
}

.chat-dropdown {
    position: relative;
    margin-bottom: 15px;
}

.chat-dropdown-toggle {
    background: none;
    border: none;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.chat-search {
    position: relative;
}

.chat-search input {
    width: 100%;
    padding: 10px 15px 10px 40px;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    background: #f8f9fa;
    font-size: 14px;
}

.chat-search input:focus {
    outline: none;
    border-color: #007bff;
    background: #fff;
}

.chat-search .search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 14px;
}

.chat-sidebar::-webkit-scrollbar {
    width: 6px;
}

.chat-sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Conversation Item Styles */
.conversation-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f1f1f1;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
}

.conversation-item:hover {
    background: #f8f9fa;
}

.conversation-item.active {
    background: #e3f2fd;
    border-right: 3px solid #007bff;
}

.conversation-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    background: #dee2e6;
    flex-shrink: 0;
    position: relative;
}

/* Online Status Indicator */
.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.online-indicator.online {
    background-color: #28a745;
}

.online-indicator.offline {
    background-color: #6c757d;
}

.conversation-info {
    flex: 1;
    min-width: 0;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.conversation-name {
    font-weight: 600;
    font-size: 15px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversation-time {
    font-size: 12px;
    color: #6c757d;
    white-space: nowrap;
    flex-shrink: 0;
}

.conversation-preview {
    font-size: 13px;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3;
}

.conversation-actions {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    display: none;
    gap: 5px;
}

.conversation-item:hover .conversation-actions {
    display: flex;
}

.conversation-item:hover .conversation-time {
    display: none;
}

.conversation-action-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: #fff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    color: #6c757d;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s;
}

.conversation-action-btn:hover {
    background: #f8f9fa;
    color: #333;
}

.conversation-action-btn.delete:hover {
    background: #dc3545;
    color: white;
}

.unread-badge {
    background: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 11px;
    min-width: 18px;
    text-align: center;
    position: absolute;
    top: 10px;
    right: 10px;
}

/* Chat Main Area Styles */
.chat-main {
    height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;
}

#chat-area {
    height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1;
}

.chat-header {
    background: #fff;
    border-bottom: 1px solid #e9ecef;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chat-header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-header-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
    background: #dee2e6;
    position: relative;
}

.chat-header-info h6 {
    margin: 0;
    font-weight: 600;
    font-size: 16px;
    color: #333;
}

.chat-header-info .last-seen {
    font-size: 12px;
    color: #6c757d;
    margin: 0;
}

.chat-header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-header-btn {
    width: 35px;
    height: 35px;
    border: none;
    background: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.2s;
}

.chat-header-btn:hover {
    background: #f8f9fa;
    color: #333;
}

/* Messages Area Styles */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    padding-bottom: 20px; /* Normal padding */
    background: #f8f9fa;
    position: relative;
    min-height: 0; /* Allow flex shrinking */
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Message Bubble Styles */
.message-bubble {
    max-width: 70%;
    margin-bottom: 15px;
    word-wrap: break-word;
    display: flex;
    flex-direction: column;
}

.message-bubble.sent {
    margin-left: auto;
    align-items: flex-end;
}

.message-bubble.received {
    margin-right: auto;
    align-items: flex-start;
}

.message-content {
    padding: 10px 15px;
    border-radius: 18px;
    position: relative;
    max-width: 100%;
}

.message-bubble.sent .message-content {
    background: #007bff;
    color: white;
    border-bottom-right-radius: 4px;
}

.message-bubble.received .message-content {
    background: #e9ecef;
    color: #333;
    border-bottom-left-radius: 4px;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 5px;
    padding: 0 5px;
}

/* Ensure received messages never show status icons */
.message-bubble.received .message-time i,
.message-bubble.received .message-time .status-icon {
    display: none !important;
}

.message-status {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 2px;
}

/* Attachment Styles */
.attachment-preview {
    max-width: 200px;
    border-radius: 8px;
    margin-top: 5px;
    cursor: pointer;
}

.file-attachment {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    margin-top: 5px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.file-attachment:hover {
    background: rgba(255,255,255,0.2);
}

.file-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Chat Input Styles */
.chat-input-container {
    background: #fff;
    border-top: 1px solid #e9ecef;
    padding: 20px;
    flex-shrink: 0; /* Don't shrink the input area */
    z-index: 10;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

.chat-input-wrapper {
    position: relative;
    display: flex;
    align-items: flex-end;
    gap: 10px;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 8px 15px;
    border: 1px solid #e9ecef;
}

.chat-input-wrapper:focus-within {
    border-color: #007bff;
    background: #fff;
}

.attachment-btn, .emoji-btn {
    width: 30px;
    height: 30px;
    border: none;
    background: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.2s;
    flex-shrink: 0;
}

.attachment-btn:hover, .emoji-btn:hover {
    background: #e9ecef;
    color: #333;
}

.message-input {
    flex: 1;
    border: none;
    background: none;
    resize: none;
    outline: none;
    font-size: 14px;
    line-height: 1.4;
    max-height: 100px;
    min-height: 20px;
    padding: 5px 0;
}

.message-input::placeholder {
    color: #6c757d;
}

.send-btn {
    width: 35px;
    height: 35px;
    border: none;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    flex-shrink: 0;
}

.send-btn:hover {
    background: #0056b3;
}

.send-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* Typing Indicator */
.typing-indicator {
    display: none;
    padding: 10px 15px;
    font-style: italic;
    color: #6c757d;
    font-size: 13px;
}

.typing-dots {
    display: inline-block;
}

.typing-dots span {
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #6c757d;
    margin: 0 1px;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    text-align: center;
    padding: 40px 20px;
}

.empty-state i {
    font-size: 48px;
    opacity: 0.3;
    margin-bottom: 16px;
    color: #dee2e6;
}

.empty-state h5 {
    color: #333;
    margin-bottom: 8px;
    font-weight: 600;
}

.empty-state p {
    color: #6c757d;
    margin: 0;
    font-size: 14px;
}

/* Load More Button */
.load-more-btn {
    text-align: center;
    padding: 10px;
    color: #007bff;
    cursor: pointer;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s;
}

.load-more-btn:hover {
    background: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-container {
        height: calc(100vh - 80px);
    }

    .message-bubble {
        max-width: 85%;
    }

    .chat-sidebar {
        position: absolute;
        left: -100%;
        width: 100%;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .chat-sidebar.show {
        left: 0;
    }

    .mobile-chat-toggle {
        display: block;
    }
}

@media (min-width: 769px) {
    .mobile-chat-toggle {
        display: none;
    }
}

/* Emoji Picker Styles */
.emoji-picker-container {
    position: absolute;
    bottom: 60px;
    right: 20px;
    z-index: 1000;
    display: none;
}

.emoji-picker-simple {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 10px;
    max-width: 300px;
    max-height: 200px;
    overflow-y: auto;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 5px;
}

.emoji-item {
    font-size: 20px;
    padding: 5px;
    cursor: pointer;
    border-radius: 4px;
    text-align: center;
    transition: background-color 0.2s;
}

.emoji-item:hover {
    background-color: #f8f9fa;
}

/* Message Counter */
.message-counter {
    position: absolute;
    top: -20px;
    right: 10px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* File Browser Modal Styles */
.file-preview-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.2s;
}

.file-preview-item:hover {
    background-color: #f8f9fa;
}

.file-preview-item.selected {
    background-color: var(--bs-primary-bg-subtle, #e7f1ff);
    border-left: 3px solid var(--bs-primary, #0d6efd);
}

.file-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
    color: white;
}

/* File type specific colors */
.file-icon.image { background: #28a745; }
.file-icon.video { background: #17a2b8; }
.file-icon.pdf { background: #dc3545; }
.file-icon.word { background: #2b579a; }
.file-icon.excel { background: #217346; }
.file-icon.powerpoint { background: #d24726; }
.file-icon.text { background: #6c757d; }
.file-icon.code { background: #6f42c1; }
.file-icon.archive { background: #fd7e14; }
.file-icon.audio { background: #e83e8c; }
.file-icon.default { background: #495057; }

.file-info h6 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #212529;
}

.file-info small {
    color: #6c757d;
    font-size: 12px;
}

.selected-file-chip {
    background: var(--bs-primary-bg-subtle, #e7f1ff);
    border: 1px solid var(--bs-primary, #0d6efd);
    border-radius: 16px;
    padding: 4px 8px;
    font-size: 11px;
    color: var(--bs-primary, #0d6efd);
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.selected-file-chip .remove {
    cursor: pointer;
    font-weight: bold;
    margin-left: 4px;
}

.selected-file-chip .remove:hover {
    color: #dc3545;
}

/* WhatsApp-style Loading Effects */
.temp-message {
    opacity: 0.8;
    animation: fadeInUp 0.3s ease-out;
}

.temp-image-container img {
    transition: filter 0.3s ease;
}

.loading-overlay {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.temp-video-container,
.temp-document-container {
    transition: opacity 0.3s ease;
}

.temp-message .message-time {
    font-style: italic;
    opacity: 0.6;
}

/* WhatsApp-style Circular Loader */
.whatsapp-loader {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: whatsappSpin 1s linear infinite;
}

.whatsapp-loader.small {
    width: 16px;
    height: 16px;
    border-width: 1.5px;
}

@keyframes whatsappSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* File upload progress animation */
@keyframes uploadProgress {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.upload-progress-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
    animation: uploadProgress 2s ease-in-out infinite;
    width: 50%;
}

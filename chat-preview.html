<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Interface Preview</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .chat-container {
            height: calc(100vh - 40px);
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            margin: 20px;
        }

        .chat-sidebar {
            background: #fff;
            border-right: 1px solid #e9ecef;
            height: 100%;
            overflow-y: auto;
            width: 320px;
            min-width: 320px;
        }

        .chat-sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background: #fff;
        }

        .chat-dropdown-toggle {
            background: none;
            border: none;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            margin-bottom: 15px;
        }

        .chat-search {
            position: relative;
        }

        .chat-search input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            background: #f8f9fa;
            font-size: 14px;
        }

        .chat-search input:focus {
            outline: none;
            border-color: #007bff;
            background: #fff;
        }

        .chat-search .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 14px;
        }

        .conversation-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f1f1;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .conversation-item:hover {
            background: #f8f9fa;
        }

        .conversation-item.active {
            background: #e3f2fd;
            border-right: 3px solid #007bff;
        }

        .conversation-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            background: #dee2e6;
            flex-shrink: 0;
        }

        .conversation-info {
            flex: 1;
            min-width: 0;
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .conversation-name {
            font-weight: 600;
            font-size: 15px;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-time {
            font-size: 12px;
            color: #6c757d;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .conversation-preview {
            font-size: 13px;
            color: #6c757d;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.3;
        }

        .conversation-actions {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            display: none;
            gap: 5px;
        }

        .conversation-item:hover .conversation-actions {
            display: flex;
        }

        .conversation-item:hover .conversation-time {
            display: none;
        }

        .conversation-action-btn {
            width: 28px;
            height: 28px;
            border: none;
            background: #fff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            color: #6c757d;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.2s;
        }

        .conversation-action-btn:hover {
            background: #f8f9fa;
            color: #333;
        }

        .conversation-action-btn.delete:hover {
            background: #dc3545;
            color: white;
        }

        .unread-badge {
            background: #007bff;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 11px;
            min-width: 18px;
            text-align: center;
            font-weight: 600;
        }

        .chat-main {
            height: 100%;
            display: flex;
            flex-direction: column;
            flex: 1;
        }

        .chat-header {
            background: #fff;
            border-bottom: 1px solid #e9ecef;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chat-header-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            object-fit: cover;
            background: #dee2e6;
        }

        .chat-header-info h6 {
            margin: 0;
            font-weight: 600;
            font-size: 16px;
            color: #333;
        }

        .chat-header-info .last-seen {
            font-size: 12px;
            color: #6c757d;
            margin: 0;
        }

        .chat-header-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chat-header-btn {
            width: 35px;
            height: 35px;
            border: none;
            background: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #6c757d;
            transition: all 0.2s;
        }

        .chat-header-btn:hover {
            background: #f8f9fa;
            color: #333;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
            position: relative;
        }

        .message-bubble {
            max-width: 70%;
            margin-bottom: 15px;
            word-wrap: break-word;
            display: flex;
            flex-direction: column;
        }

        .message-bubble.sent {
            margin-left: auto;
            align-items: flex-end;
        }

        .message-bubble.received {
            margin-right: auto;
            align-items: flex-start;
        }

        .message-content {
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
            max-width: 100%;
            font-size: 14px;
            line-height: 1.4;
        }

        .message-bubble.sent .message-content {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message-bubble.received .message-content {
            background: #e9ecef;
            color: #333;
            border-bottom-left-radius: 4px;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 4px;
            padding: 0 4px;
        }

        .chat-input-container {
            background: #fff;
            border-top: 1px solid #e9ecef;
            padding: 20px;
        }

        .chat-input-wrapper {
            position: relative;
            display: flex;
            align-items: flex-end;
            gap: 10px;
            background: #f8f9fa;
            border-radius: 25px;
            padding: 8px 15px;
            border: 1px solid #e9ecef;
        }

        .chat-input-wrapper:focus-within {
            border-color: #007bff;
            background: #fff;
        }

        .attachment-btn {
            width: 30px;
            height: 30px;
            border: none;
            background: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #6c757d;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .attachment-btn:hover {
            background: #e9ecef;
            color: #333;
        }

        .message-input {
            flex: 1;
            border: none;
            background: none;
            resize: none;
            outline: none;
            font-size: 14px;
            line-height: 1.4;
            max-height: 100px;
            min-height: 20px;
            padding: 5px 0;
        }

        .message-input::placeholder {
            color: #6c757d;
        }

        .send-btn {
            width: 35px;
            height: 35px;
            border: none;
            background: #007bff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .send-btn:hover {
            background: #0056b3;
        }

        /* Scrollbar Styles */
        .chat-sidebar::-webkit-scrollbar,
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-sidebar::-webkit-scrollbar-track,
        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .chat-sidebar::-webkit-scrollbar-thumb,
        .chat-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .chat-sidebar::-webkit-scrollbar-thumb:hover,
        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="chat-container d-flex">
        <!-- Chat Sidebar -->
        <div class="chat-sidebar">
            <div class="chat-sidebar-header">
                <button class="chat-dropdown-toggle" type="button">
                    All Chats
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="chat-search">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" placeholder="Search conversations..." autocomplete="off">
                </div>
            </div>
            
            <!-- Conversation List -->
            <div class="conversation-item active">
                <img src="https://via.placeholder.com/50x50/007bff/ffffff?text=ZV" alt="Zain Vaccaro" class="conversation-avatar">
                <div class="conversation-info">
                    <div class="conversation-header">
                        <div class="conversation-name">Zain Vaccaro</div>
                        <div class="conversation-time">35m</div>
                    </div>
                    <div class="conversation-preview">Thank you for ordering with me! I truly hope you're happy with the delivery. If there's anything you would like to enquire more, don't hesitate to reach out anymore. Looking forward to working with you in the future!</div>
                </div>
                <div class="conversation-actions">
                    <button class="conversation-action-btn archive" title="Archive">
                        <i class="fas fa-archive"></i>
                    </button>
                    <button class="conversation-action-btn delete" title="Delete Chat">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>

            <div class="conversation-item">
                <img src="https://via.placeholder.com/50x50/28a745/ffffff?text=CC" alt="Charlie Culhane" class="conversation-avatar">
                <div class="conversation-info">
                    <div class="conversation-header">
                        <div class="conversation-name">Charlie Culhane</div>
                        <div class="conversation-time">1h</div>
                    </div>
                    <div class="conversation-preview">Lorem ipsum dolor sit amet...</div>
                </div>
                <div class="conversation-actions">
                    <button class="conversation-action-btn archive" title="Archive">
                        <i class="fas fa-archive"></i>
                    </button>
                    <button class="conversation-action-btn delete" title="Delete Chat">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>

            <div class="conversation-item">
                <img src="https://via.placeholder.com/50x50/dc3545/ffffff?text=RE" alt="Ryan Ekstrom" class="conversation-avatar">
                <div class="conversation-info">
                    <div class="conversation-header">
                        <div class="conversation-name">Ryan Ekstrom</div>
                        <div class="conversation-time">1h</div>
                    </div>
                    <div class="conversation-preview">Lorem ipsum dolor sit amet...</div>
                    <div class="mt-1"><span class="unread-badge">2</span></div>
                </div>
                <div class="conversation-actions">
                    <button class="conversation-action-btn archive" title="Archive">
                        <i class="fas fa-archive"></i>
                    </button>
                    <button class="conversation-action-btn delete" title="Delete Chat">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Main Area -->
        <div class="chat-main">
            <div class="chat-header">
                <div class="chat-header-left">
                    <img src="https://via.placeholder.com/45x45/007bff/ffffff?text=ZV" alt="Zain Vaccaro" class="chat-header-avatar">
                    <div class="chat-header-info">
                        <h6>Zain Vaccaro</h6>
                        <p class="last-seen">Last seen 35 minutes ago</p>
                    </div>
                </div>
                <div class="chat-header-actions">
                    <button type="button" class="chat-header-btn" title="More options">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
            </div>

            <div class="chat-messages">
                <div class="message-bubble received">
                    <div class="message-content">
                        Thank you for ordering with me!
                    </div>
                    <div class="message-time">31 Mar 2023, 17:08</div>
                </div>

                <div class="message-bubble received">
                    <div class="message-content">
                        I truly hope you're happy with the delivery. If there's anything you would like to enquire more, don't hesitate to reach out anymore. Looking forward to working with you in the future!
                    </div>
                    <div class="message-time">31 Mar 2023, 17:08</div>
                </div>

                <div class="message-bubble sent">
                    <div class="message-content">
                        Thank you for ordering with me! I truly hope you're happy with the delivery. If there's anything you would like to enquire more, don't hesitate to reach out anymore. Looking forward to working with you in the future!
                    </div>
                    <div class="message-time">31 Mar 2023, 17:08</div>
                </div>
            </div>

            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <button type="button" class="attachment-btn" title="Attach files">
                        <i class="fas fa-plus"></i>
                    </button>
                    <textarea class="message-input" placeholder="Write message here..." rows="1"></textarea>
                    <button type="button" class="send-btn" title="Send message">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simple demo functionality
        document.querySelectorAll('.conversation-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.conversation-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Auto-resize textarea
        const messageInput = document.querySelector('.message-input');
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    </script>
</body>
</html>

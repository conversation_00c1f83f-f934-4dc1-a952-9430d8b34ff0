<?php

namespace App\Http\Controllers;

use App\Mail\EmailVerificationMail;
use App\Mail\UserPasswordMail;
use App\Models\EmailVerification;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;

class EmailVerificationController extends Controller
{
    public function sendOtp(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'email' => ['required', 'regex:' . config('constant.email_regex')],
            'user_type' => 'required|in:customer,professional',
        ]);
        if ($validation->fails()) {
            return api_response(false, $validation->errors()->first());
        }

        $email = $request->email;
        $selectedUserType = $request->user_type;

        // Check if user already exists
        $existingUser = \App\Models\User::where('email', $email)->first();

        if ($existingUser) {
            // Email exists - check if user has the correct role
            $userRole = $existingUser->getRoleNames()->first();

            // Check if the selected user type matches the existing user's role
            if ($selectedUserType == "professional" && !in_array($userRole, ["individual", "business", "professional"])) {
                return api_response(false, "This email is registered as a {$userRole}. Please select the correct account type or use a different email.");
            } elseif ($selectedUserType == "customer" && $userRole != "customer") {
                return api_response(false, "This email is registered as a {$userRole}. Please select the correct account type or use a different email.");
            }

            // Email exists with correct role - this is a login scenario
            return api_response(true, "Email found. Please enter your password.", [
                'scenario' => 'login',
                'user_exists' => true,
                'user_role' => $userRole
            ]);
        }

        // Email doesn't exist - this is a registration scenario, send OTP
        $plainToken = rand(100000, 999999);
        $encryptedToken = encrypt($plainToken);
        EmailVerification::updateOrCreate(
            ['email' => $email],
            [
                'token' => $encryptedToken,
                'expires_at' => now()->addMinutes(2),
            ]
        );
        Mail::to($email)->send(new EmailVerificationMail($plainToken));
        return api_response(true, "OTP Sent", [
            'scenario' => 'register',
            'user_exists' => false
        ]);
    }

    public function verifyOtp(Request $request)
    {
        DB::beginTransaction();
        try {
            $validation = Validator::make($request->all(), [
                'email' => ['required','regex:' . config('constant.email_regex'),'exists:email_verifications,email'],
                'otp' => 'required|digits:6',
                "user_type" => 'required|in:customer,professional',
            ]);
            if ($validation->fails()) {
                return api_response(false, $validation->errors()->first());
            }
            $record = EmailVerification::where('email', $request->email)->first();

            if (!$record) {
                return api_response(false, "OTP not found");
            }

            if (Carbon::parse($record->expires_at)->isPast()) {
                return api_response(false, "OTP expired");
            }

            try {
                $decryptedToken = decrypt($record->token);
            } catch (\Exception $e) {
                return api_response(false, "Invalid OTP token");
            }
            if ($decryptedToken != $request->otp) {
                return api_response(false, "Invalid OTP");
            }
            $record->delete();

            $password = Str::random(8);
            $user = new User();
            $user->email = $request->email;
            $user->password = Hash::make($password);
            $user->email_verified_at = now();
            $user->save();

            $role = Role::where("name", $request->user_type)->first();
            $user->assignRole($role);
            DB::commit();

            Auth::login($user);
            Mail::to($user->email)->send(new UserPasswordMail($password));

            // Build registration URL with invitation parameters if they exist
            $registrationUrl = route('register.user_type', ["user_type" => $request->user_type]);
            if ($request->has('invited_by')) {
                $registrationUrl .= '?invited_by=' . $request->invited_by;
                if ($request->has('invitation_email')) {
                    $registrationUrl .= '&email=' . urlencode($request->invitation_email);
                }
            }

            return api_response(true, "OTP verified successfully", ["url" => $registrationUrl]);
        } catch (\Throwable $th) {
            DB::rollback();
            return api_response(false, "Something went wrong");
        }
    }

    public function resendOtp(Request $request)
    {
        $validation = Validator::make($request->all(), [
            'email' => ['required', 'regex:' . config('constant.email_regex')],
        ]);

        if ($validation->fails()) {
            return api_response(false, $validation->errors()->first());
        }

        $email = $request->email;

        // Generate new OTP
        $plainToken = rand(100000, 999999);
        $encryptedToken = encrypt($plainToken);

        EmailVerification::updateOrCreate(
            ['email' => $email],
            [
                'token' => $encryptedToken,
                'expires_at' => now()->addMinutes(2),
            ]
        );

        Mail::to($email)->send(new EmailVerificationMail($plainToken));

        return api_response(true, "OTP resent successfully");
    }
}

<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Booking extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'booking_id',
        'booking_number',
        'user_id',
        'service_id',
        'provider_id',
        'booking_date',
        'booking_time',
        'duration',
        'service_price',
        'price',
        'additional_cost',
        'vat_amount',
        'discount_amount',
        'total_amount',
        'location_type',
        'address',
        'latitude',
        'longitude',
        'comments',
        'required_items',
        'coupon_id',
        'coupon_code',
        'payment_method',
        'payment_status',
        'stripe_payment_intent_id',
        'stripe_session_id',
        'status',
        'confirmed_at',
    ];

    /**
     * Get the service that owns the booking.
     */
    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the customer that owns the booking.
     */
    public function customer()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the provider that owns the booking.
     */
    public function provider()
    {
        return $this->belongsTo(User::class, 'provider_id');
    }

    /**
     * Get the booking services (professionals assigned to this booking).
     */
    public function bookingServices()
    {
        return $this->hasMany(BookingService::class);
    }

    /**
     * Get the assigned professionals through the pivot table.
     */
    public function assignedProfessionals()
    {
        return $this->hasManyThrough(
            Staff::class,
            BookingService::class,
            'booking_id', // Foreign key on booking_service table
            'id', // Foreign key on staffs table
            'id', // Local key on bookings table
            'staff_id' // Local key on booking_service table
        );
    }

    /**
     * Generate a unique booking ID
     */
    public static function generateBookingId()
    {
        do {
            $bookingId = 'BK' . strtoupper(uniqid());
        } while (self::where('booking_id', $bookingId)->exists());

        return $bookingId;
    }

    /**
     * Get formatted booking date
     */
    public function getFormattedBookingDateAttribute()
    {
        return $this->booking_date ? date('M d, Y', strtotime($this->booking_date)) : null;
    }

    /**
     * Get formatted booking time
     */
    public function getFormattedBookingTimeAttribute()
    {
        return $this->booking_time ? date('h:i A', strtotime($this->booking_time)) : null;
    }

    /**
     * Check if the booking time has passed
     */
    public function hasTimePassed()
    {
        if (!$this->booking_date || !$this->booking_time) {
            return false;
        }

        $bookingDateTime = $this->booking_date . ' ' . $this->booking_time;
        return now() > $bookingDateTime;
    }

    /**
     * Get the selected professionals for this booking
     */
    public function getSelectedProfessionalsAttribute()
    {
        return $this->bookingServices()->with(['staff', 'user'])->get()->map(function ($bookingService) {
            return [
                'id' => $bookingService->staff_id,
                'name' => $bookingService->staff->name,
                'email' => $bookingService->staff->email,
                'phone' => $bookingService->staff->phone,
                'image' => $bookingService->staff->image,
                'user_id' => $bookingService->user_id,
                'user' => $bookingService->user
            ];
        });
    }
}

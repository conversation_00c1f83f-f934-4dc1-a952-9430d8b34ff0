@if($unapproved_users->count() > 0)
    @foreach ($unapproved_users as $user)
        <tr>
            <td>
                <div class="card  flex-row shadow-none p-0 gap-3 align-items-center">
                    <div class="card-header p-0 border-0 align-items-start">
                        <img src="{{ asset('website') . '/' . ($user->profile->avatar ?? 'assets/images/dummy-avatar.png') }}"
                            class="h-80px w-80px rounded-3 object-fit-contain" alt="card-image" />
                    </div>
                    <div class="card-body p-0">
                        <p class="fs-16 regular black m-0 pb-5">
                            {{ $user->name }}</p>
                    </div>
                </div>
            </td>
            <td data-label="EMAIL ADDRESS">{{ $user->email }}</td>
            <td data-label="STATUS" class="professional-status status paid-status">
                {{ $user->approval == 0 ? 'Pending' : 'Approved' }}
            </td>
            <td data-label="JOINED DATE">
                {{ $user->created_at->format('M d, Y') }}</td>
            <td data-label="">
                <div class="dropdown">
                    <button class="drop-btn" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        <i class="bi bi-three-dots-vertical"></i>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        <li>
                            <a class="dropdown-item complete fs-14 regular "
                                href="{{ route('professional.approve', $user->ids) }}">
                                <i class="bi bi-check-circle complete-icon"></i>
                                Approve
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item complete fs-14 regular " href="{{ route('professional.show', $user->ids) }}">
                                <i class="bi bi-check-circle complete-icon"></i>
                                View Profile
                            </a>
                        </li>
                        <li>
                            <button class="dropdown-item cancel fs-14 regular reject-professional"
                                    type="button"
                                    data-user-id="{{ $user->ids }}"
                                    data-user-name="{{ $user->name }}"
                                    data-user-email="{{ $user->email }}">
                                <i class="fa-solid fa-xmark cancel-icon"></i> Reject
                            </button>
                        </li>
                    </ul>
                </div>
            </td>
        </tr>
    @endforeach
@else
    <tr>
        <td colspan="6" class="text-center py-5">
            <div class="d-flex flex-column align-items-center">
                <i class="fas fa-user-clock fa-3x text-muted mb-3"></i>
                <h5 class="text-muted mb-2">No pending professionals found</h5>
                <p class="text-muted">Try adjusting your search criteria or filters</p>
            </div>
        </td>
    </tr>
@endif

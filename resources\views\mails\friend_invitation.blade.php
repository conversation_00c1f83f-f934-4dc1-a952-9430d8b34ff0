<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Friend Invitation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333333;
            text-align: center;
        }
        p {
            color: #555555;
            line-height: 1.6;
        }
        .invitation-button {
            display: inline-block;
            background-color: #007bff;
            color: #ffffff;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
        }
        .invitation-button:hover {
            background-color: #0056b3;
        }
        .footer {
            margin-top: 30px;
            font-size: 14px;
            color: #888888;
        }
        .text-center {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>You're Invited to Join Our Platform!</h1>
        <p>Hello!</p>
        <p><strong>{{ $senderName }}</strong> has invited you to join our platform and wants to connect with you as a friend.</p>
        <p>To accept this invitation and create your account, please click the button below:</p>
        <div class="text-center">
            <a href="{{ $registrationUrl }}" class="invitation-button">Join Now & Connect</a>
        </div>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p><a href="{{ $registrationUrl }}">{{ $registrationUrl }}</a></p>
        <p>Once you register, you'll be able to connect with {{ $senderName }} and explore all the features our platform has to offer.</p>
        <p class="footer">
            Thanks,<br />
            {{ config('app.name') }} Team
        </p>
    </div>
</body>
</html>

<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class EarningsExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    protected $bookings;

    public function __construct($bookings)
    {
        $this->bookings = $bookings;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->bookings;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Customer Name',
            'Service Type',
            'Service Name',
            'Amount',
            'Status',
            'Date',
            'Booking ID'
        ];
    }

    /**
     * @param mixed $booking
     * @return array
     */
    public function map($booking): array
    {
        // Get status text
        $status = $this->getStatusText($booking->status, $booking);
        
        // Format date
        $date = \Carbon\Carbon::parse($booking->booking_date)->format('M d, Y');

        return [
            $booking->customer->name ?? 'N/A',
            $booking->service->category->name ?? 'N/A',
            $booking->service->name ?? 'N/A',
            '$' . ($booking->price ?? $booking->total_amount ?? 0),
            $status,
            $date,
            $booking->booking_number ?? 'N/A'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }

    /**
     * Get status text based on booking status and time
     */
    private function getStatusText($status, $booking)
    {
        switch ($status) {
            case 0:
                if ($booking->hasTimePassed()) {
                    return 'Ongoing';
                } else {
                    return 'Upcoming';
                }
            case 1:
                return 'Completed';
            case 2:
                return 'Cancelled';
            default:
                return 'Unknown';
        }
    }
}

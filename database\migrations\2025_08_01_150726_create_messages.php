<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->integer('conversation_id')->nullable();
            $table->integer('sender_id')->nullable();
            $table->string('message_type')->nullable();
            $table->text('content')->nullable();
            $table->longText('attachments')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->integer('is_edited')->default(0);
            $table->timestamp('edited_at')->nullable();
            $table->enum('sending_status', ['sending','sent', 'delivered', 'read'])->default('sending');
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};

<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ProfessionalsExport;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class ProfessionalController extends Controller
{
    function index()
    {
        $approved_users = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['professional', "individual", "business"]);
        })->where("approval", 1)->where("registration_completed", 1)->latest()->paginate(10);

        $unapproved_users = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['professional']);
        })->where("approval", 0)->where("registration_completed", 1)->latest()->paginate(10);
        return view('professional.index', compact('unapproved_users', 'approved_users'));
    }

    function approve($ids)
    {
        $user = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['professional']);
        })->where('ids', $ids)->firstOrFail();
        if ($user->approval == 1) {
            return redirect()->back()->with(['title' => 'Error', 'message' => 'Professional already approved', 'type' => 'error']);
        }
        $user->approval = 1;
        $user->save();
        return redirect()->back()->with(['title' => 'Done', 'message' => 'Professional approved successfully', 'type' => 'success']);
    }

    function reject($ids)
    {
        try {
            DB::beginTransaction();

            $user = User::whereHas('roles', function ($q) {
                $q->whereIn('name', ['professional']);
            })->where('ids', $ids)->firstOrFail();

            if ($user->approval == 1) {
                return redirect()->back()->with(['title' => 'Error', 'message' => 'Professional is already approved', 'type' => 'error']);
            }

            // Send rejection email
            $this->sendRejectionEmail($user);

            // Delete all related data
            $this->deleteAllUserData($user);

            DB::commit();

            return redirect()->back()->with(['title' => 'Done', 'message' => 'Professional rejected and all data deleted successfully', 'type' => 'success']);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Professional rejection failed: ' . $e->getMessage());
            return redirect()->back()->with(['title' => 'Error', 'message' => 'Failed to reject professional: ' . $e->getMessage(), 'type' => 'error']);
        }
    }
    function changeStatus($ids)
    {
        $user = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['professional', "individual", "business"]);
        })->where('ids', $ids)->firstOrFail();

        if ($user->status == 1) {
            $user->status = 0;
            $message = 'Professional deactivated successfully';
        } else {
            $user->status = 1;
            $message = 'Professional activated successfully';
        }
        $user->save();
        return redirect()->back()->with(['title' => 'Done', 'message' => $message, 'type' => 'success']);
    }

    /**
     * Export approved professionals to Excel
     */
    public function exportApproved()
    {
        $approved_users = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['professional', "individual", "business"]);
        })->where("approval", 1)->where("registration_completed", 1)->with(['profile'])->get();

        return Excel::download(new ProfessionalsExport($approved_users, 'approved'), 'approved_professionals_' . date('Y-m-d') . '.xlsx');
    }

    /**
     * Export unapproved professionals to Excel
     */
    public function exportUnapproved()
    {
        $unapproved_users = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['professional']);
        })->where("approval", 0)->where("registration_completed", 1)->with(['profile'])->get();

        return Excel::download(new ProfessionalsExport($unapproved_users, 'unapproved'), 'unapproved_professionals_' . date('Y-m-d') . '.xlsx');
    }

    /**
     * Export all professionals to Excel
     */
    public function exportAll()
    {
        $all_users = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['professional', "individual", "business"]);
        })->where("registration_completed", 1)->with(['profile'])->get();

        return Excel::download(new ProfessionalsExport($all_users, 'all'), 'all_professionals_' . date('Y-m-d') . '.xlsx');
    }

    public function show($id)
    {
        $user = User::where('ids', $id)
            ->with([
                'categories.category',
                'categories.subcategory',
                'profile',
                'socials',
                'galleries',
                'product_cerficates',
                'certificates',
                'openingHours',
                'allHolidays',
                'introCards'
            ])
            ->firstOrFail();
        return view('professional.show', compact('user'));
    }

    /**
     * Send rejection email to the professional
     */
    private function sendRejectionEmail($user)
    {
        try {
            $subject = 'Professional Registration Rejected';
            $message = "
                <h2>Registration Rejected</h2>
                <p>Dear {$user->name},</p>
                <p>We regret to inform you that your professional registration request has been rejected.</p>
                <p>After careful review, we were unable to approve your application at this time.</p>
                <p>If you have any questions or would like to reapply in the future, please feel free to contact our support team.</p>
                <p>Thank you for your interest in our platform.</p>
                <br>
                <p>Best regards,<br>The Team</p>
            ";

            Mail::html($message, function ($mail) use ($user, $subject) {
                $mail->to($user->email, $user->name)
                     ->subject($subject);
            });

            Log::info("Rejection email sent to: {$user->email}");
        } catch (\Exception $e) {
            Log::error("Failed to send rejection email to {$user->email}: " . $e->getMessage());
        }
    }

    /**
     * Delete all user related data from every table
     */
    private function deleteAllUserData($user)
    {
        try {
            if ($user->profile && $user->profile->pic) {
                $this->deleteImage($user->profile->pic);
            }
            if ($user->profile && $user->profile->banner_image) {
                $this->deleteImage($user->profile->banner_image);
            }
            if ($user->galleries) {
                foreach ($user->galleries as $gallery) {
                    if ($gallery->image) {
                        $this->deleteImage($gallery->image);
                    }
                }
            }
            if ($user->certificates) {
                foreach ($user->certificates as $certificate) {
                    if ($certificate->image) {
                        $this->deleteImage($certificate->image);
                    }
                }
            }
            // Delete all related data in order (respecting foreign key constraints)
            $user->introCards()->delete();
            $user->galleries()->delete();
            $user->socials()->delete();
            $user->certificates()->delete();
            $user->allHolidays()->delete();
            $user->openingHours()->delete();
            $user->userCategories()->delete();
            $user->categories()->detach();
            $user->subcategories()->detach();
            $user->product_cerficates()->detach();
            if ($user->profile) {
                $user->profile()->delete();
            }
            $user->roles()->detach();
            $user->delete();
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function filterProfessionals(Request $request)
    {
        $tab = $request->get('tab', 'approved'); // 'approved' or 'pending'
        // Only get values if they are actually provided and not empty
        $search = $request->filled('search') ? trim($request->get('search')) : '';
        $status = $request->filled('status') && $request->get('status') !== 'all' ? $request->get('status') : '';
        $date = $request->filled('date') ? trim($request->get('date')) : '';
        $dateFilter = $request->filled('date_filter') ? $request->get('date_filter') : null;

        // Base query for approved users (All Professionals tab)
        if ($tab === 'approved') {
            $query = User::whereHas('roles', function ($q) {
                $q->whereIn('name', ['professional', "individual", "business"]);
            })->where("approval", 1)->where("registration_completed", 1);
        } else {
            // Base query for pending users (New Requests tab)
            $query = User::whereHas('roles', function ($q) {
                $q->whereIn('name', ['professional']);
            })->where("approval", 0)->where("registration_completed", 1);
        }

        // Only apply search filter if search term is provided
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // Only apply status filter if status is provided and not 'all'
        if (!empty($status)) {
            if ($tab === 'approved') {
                switch ($status) {
                    case 'active':
                        $query->where('status', 1);
                        break;
                    case 'inactive':
                        $query->where('status', 0);
                        break;
                }
            } else {
                // For pending tab, all are pending by default
                if ($status === 'pending') {
                    $query->where('approval', 0);
                }
            }
        }

        // Only apply date filter if date information is provided
        if (!empty($dateFilter) && is_array($dateFilter)) {
            try {
                $startDate = $dateFilter['start_date'] ?? null;
                $endDate = $dateFilter['end_date'] ?? null;
                $filterType = $dateFilter['type'] ?? 'single';

                if (!empty($startDate)) {
                    $parsedStartDate = Carbon::parse($startDate)->format('Y-m-d');

                    if ($filterType === 'range' && !empty($endDate)) {
                        $parsedEndDate = Carbon::parse($endDate)->format('Y-m-d');
                        Log::info('Professional date range filter applied', [
                            'start_date' => $parsedStartDate,
                            'end_date' => $parsedEndDate,
                            'original' => $dateFilter
                        ]);
                        $query->whereDate('created_at', '>=', $parsedStartDate)
                              ->whereDate('created_at', '<=', $parsedEndDate);
                    } else {
                        Log::info('Professional single date filter applied', [
                            'date' => $parsedStartDate,
                            'original' => $dateFilter
                        ]);
                        $query->whereDate('created_at', $parsedStartDate);
                    }
                }
            } catch (\Exception $e) {
                Log::error('Professional date filter parsing failed', ['date_filter' => $dateFilter, 'error' => $e->getMessage()]);
                // Invalid date format, ignore filter
            }
        } elseif (!empty($date)) {
            // Fallback to old date handling for backward compatibility
            try {
                $filterDate = Carbon::parse($date)->format('Y-m-d');
                Log::info('Professional fallback date filter applied', ['original_date' => $date, 'parsed_date' => $filterDate]);
                $query->whereDate('created_at', $filterDate);
            } catch (\Exception $e) {
                Log::error('Professional fallback date parsing failed', ['date' => $date, 'error' => $e->getMessage()]);
                // Invalid date format, ignore filter
            }
        }

        $users = $query->with(['profile', 'roles'])->paginate(10);

        if ($tab === 'approved') {
            $html = view('professional.partials.approved-table', ['approved_users' => $users])->render();
        } else {
            $html = view('professional.partials.pending-table', ['unapproved_users' => $users])->render();
        }

        // Generate pagination HTML
        $paginationHtml = '';
        if ($users->hasPages()) {
            $paginationHtml = $users->links('pagination::bootstrap-4')->render();
        }

        Log::info('Professional filter results', [
            'tab' => $tab,
            'count' => $users->count(),
            'total' => $users->total(),
            'has_pages' => $users->hasPages(),
            'current_page' => $users->currentPage(),
            'last_page' => $users->lastPage(),
            'per_page' => $users->perPage(),
            'pagination_html_length' => strlen($paginationHtml)
        ]);

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => $paginationHtml,
            'count' => $users->count(),
            'total' => $users->total(),
            'has_pages' => $users->hasPages()
        ]);
    }
}

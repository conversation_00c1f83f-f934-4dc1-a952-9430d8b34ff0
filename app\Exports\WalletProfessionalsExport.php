<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class WalletProfessionalsExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    protected $professionals;

    public function __construct($professionals)
    {
        $this->professionals = $professionals;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->professionals;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Professional Name',
            'Email Address',
            'Role',
            'Total Bookings',
            'Completed Bookings',
            'Total Earned',
            'Join Date'
        ];
    }

    /**
     * @param mixed $professional
     * @return array
     */
    public function map($professional): array
    {
        $totalBookings = $professional->providedBookings->count();
        $completedBookings = $professional->providedBookings->where('status', 1)->count();
        $totalEarned = $professional->providedBookings->where('status', 1)->sum('total_amount');
        $joinDate = \Carbon\Carbon::parse($professional->created_at)->format('M d, Y');
        $role = $professional->roles->first()->name ?? 'N/A';

        return [
            $professional->name ?? 'N/A',
            $professional->email ?? 'N/A',
            ucfirst($role),
            $totalBookings,
            $completedBookings,
            '$' . number_format($totalEarned, 2),
            $joinDate
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }
}

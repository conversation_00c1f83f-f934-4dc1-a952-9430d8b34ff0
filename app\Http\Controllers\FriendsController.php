<?php

namespace App\Http\Controllers;

use App\Http\Requests\FriendAddRequest;
use App\Models\Friend;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class FriendsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $friendsQuery = Friend::where(function ($query) {
            $query->where('user_id', auth()->id())
                ->orWhere('friend_user_id', auth()->id());
        })
            ->where('status', 'accepted')
            ->with(['friendUser', 'user'])
            ->get();

        $friends = $friendsQuery->map(function ($friend) {
            if ($friend->user_id == auth()->id()) {
                if ($friend->type == 'above-13' && $friend->friendUser) {
                    $friend->display_name = $friend->friendUser->name;
                    $friend->display_user = $friend->friendUser;
                } else {
                    $friend->display_name = $friend->name;
                    $friend->display_user = null;
                }
            } else {
                $friend->display_name = $friend->user->name;
                $friend->display_user = $friend->user;
            }
            return $friend;
        });
        $pending_friends = Friend::where('friend_user_id', auth()->id())
            ->where('status', 'pending')
            ->with(['user'])
            ->get();

        return view('website.friends.index', compact('friends', 'pending_friends'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $customers = User::customer()->where('id', '!=', auth()->id())->get();
        $services = Service::all();
        return view('website.friends.create', compact('customers', 'services'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            DB::beginTransaction();
            $role = $request->role;
            $friend = new Friend();
            $friend->user_id = auth()->id();
            $friend->type = $role;
            if ($role == "under-13") {
                $friend->friend_user_id = null;
                if ($request->hasFile('profile_pic')) {
                    $friend->profile_pic  = $this->storeImage('friends', $request->file('profile_pic'));
                }
                $friend->name = $request->name;
                $friend->relationship = $request->relationship_under13;
                $friend->service_preferences = json_encode($request->service_preferences ?? []);
                $friend->status = 'accepted';
            }
            if ($role == "above-13") {
                // Check if user exists with the provided email
                $selectedUser = User::where('email', $request->friend_email)
                    ->whereHas('roles', function ($q) {
                        $q->where('name', 'customer');
                    })->first();

                if ($selectedUser) {
                    // User exists and is a customer - send friend request
                    $existingFriendship = Friend::where(function ($query) use ($selectedUser) {
                        $query->where('user_id', auth()->id())
                            ->where('friend_user_id', $selectedUser->id);
                    })
                        ->orWhere(function ($query) use ($selectedUser) {
                            $query->where('user_id', $selectedUser->id)
                                ->where('friend_user_id', auth()->id());
                        })
                        ->first();

                    if ($existingFriendship) {
                        return back()->with([
                            'type' => 'error',
                            'message' => 'Friendship already exists with this user',
                            'title' => 'Error'
                        ]);
                    }

                    $friend->friend_user_id = $selectedUser->id;
                    $friend->relationship = $request->relationship_above13;
                    $friend->name = $selectedUser->name;
                    $friend->status = 'pending';

                    // Send notification to the friend that a request was sent
                    $senderImage = auth()->user()->profile->pic ?? 'no_avatar.jpg';
                    $this->user_notification(
                        $selectedUser->id,
                        'Friend Request Received',
                        auth()->user()->name . ' has sent you a friend request!',
                        $senderImage
                    );
                } else {
                    // Check if user exists but is not a customer
                    $existingUser = User::where('email', $request->friend_email)->first();
                    if ($existingUser) {
                        return back()->with([
                            'type' => 'error',
                            'message' => 'The user must be a customer to send friend requests',
                            'title' => 'Error'
                        ]);
                    }

                    // User doesn't exist - send invitation email and store entry without friend_user_id
                    $friend->friend_user_id = null;
                    $friend->relationship = $request->relationship_above13;
                    $friend->name = $request->friend_email; // Store email as name temporarily
                    $friend->status = 'pending';

                    // Send invitation email
                    $registrationUrl = 'https://anders.democustomprojects.com/register?invited_by=' . auth()->id() . '&email=' . urlencode($request->friend_email);
                    Mail::to($request->friend_email)->send(new \App\Mail\FriendInvitationMail(auth()->user()->name, $registrationUrl));
                }
            }
            $friend->save();
            DB::commit();
            return redirect()->route('friends.index')->with([
                'type' => 'success',
                'message' => 'Friend added successfully',
                'title' => 'Created'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => 'Failed to add friend: ' . $e->getMessage(),
                'title' => 'Error'
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $friend = Friend::where('ids', $id)->firstOrFail();
        return response()->json($friend);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $friend = Friend::where('ids', $id)->firstOrFail();
        $customers = User::customer()->get();
        $services = Service::all();
        return view('website.friends.create', compact('customers', 'friend', 'services'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            DB::beginTransaction();
            $friend = Friend::where('ids', $id)->firstOrFail();
            $role = $request->role;
            $oldType = $friend->type;
            $friend->type = $role;
            if ($role == "under-13") {
                $friend->friend_user_id = null;
                if ($request->hasFile('profile_pic')) {
                    if ($friend->profile_pic) {
                        $this->deleteImage($friend->profile_pic);
                    }
                    $friend->profile_pic = $this->storeImage('friends', $request->file('profile_pic'));
                }
                $friend->name = $request->name;
                $friend->relationship = $request->relationship;
                $friend->service_preferences = json_encode($request->service_preferences ?? []);
            }
            if ($role == "above-13") {
                // Check if user exists with the provided email
                $selectedUser = User::where('email', $request->friend_email)
                    ->whereHas('roles', function ($q) {
                        $q->where('name', 'customer');
                    })->first();

                if ($selectedUser) {
                    // User exists and is a customer - update friend request
                    $existingFriendship = Friend::where('user_id', auth()->id())
                        ->where('friend_user_id', $selectedUser->id)
                        ->where('id', '!=', $friend->id)
                        ->first();
                    if ($existingFriendship) {
                        return back()->with([
                            'type' => 'error',
                            'message' => 'You are already friends with this user',
                            'title' => 'Error'
                        ]);
                    }
                    $friend->friend_user_id = $selectedUser->id;
                    $friend->relationship = $request->relationship_above13;
                    $friend->name = $selectedUser->name;
                    $friend->status = 'pending';
                } else {
                    // Check if user exists but is not a customer
                    $existingUser = User::where('email', $request->friend_email)->first();
                    if ($existingUser) {
                        return back()->with([
                            'type' => 'error',
                            'message' => 'The user must be a customer to send friend requests',
                            'title' => 'Error'
                        ]);
                    }

                    // User doesn't exist - update to invitation status
                    $friend->friend_user_id = null;
                    $friend->relationship = $request->relationship_above13;
                    $friend->name = $request->friend_email; // Store email as name temporarily
                    $friend->status = 'pending';

                    // Send invitation email
                    $registrationUrl = 'https://anders.democustomprojects.com/register?invited_by=' . auth()->id() . '&email=' . urlencode($request->friend_email);
                    Mail::to($request->friend_email)->send(new \App\Mail\FriendInvitationMail(auth()->user()->name, $registrationUrl));
                }

                if ($oldType == "under-13" && $friend->profile_pic) {
                    $this->deleteImage($friend->profile_pic);
                    $friend->profile_pic = null;
                }
                $friend->service_preferences = null;
            }
            $friend->save();
            DB::commit();
            return redirect()->route('friends.index')->with([
                'type' => 'success',
                'message' => 'Friend updated successfully',
                'title' => 'Updated'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => 'Failed to update friend: ' . $e->getMessage(),
                'title' => 'Error'
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            DB::beginTransaction();
            $friend = Friend::where('ids', $id)->firstOrFail();
            if ($friend->user_id !== auth()->id()) {
                return back()->with([
                    'type' => 'error',
                    'message' => 'You are not authorized to delete this friend',
                    'title' => 'Error'
                ]);
            }
            if ($friend->type == "under-13" && $friend->profile_pic) {
                $this->deleteImage($friend->profile_pic);
            }
            $friend->delete();
            DB::commit();
            return redirect()->back()->with([
                'type' => 'success',
                'message' => 'Friend deleted successfully',
                'title' => 'Deleted'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with([
                'type' => 'error',
                'message' => 'Failed to delete friend: ' . $e->getMessage(),
                'title' => 'Error'
            ]);
        }
    }

    /**
     * Accept a friend request
     */
    public function accept(string $id)
    {
        try {
            DB::beginTransaction();
            $friend = Friend::where('ids', $id)->firstOrFail();
            $friend->status = 'accepted';
            $friend->save();
            // Get the current user's image who accepted the request
            $accepterImage = auth()->user()->profile->pic ?? 'no_avatar.jpg';

            $this->user_notification($friend->user_id,'Friend Request Accepted',auth()->user()->name . ' has accepted your friend request!', $accepterImage);
            DB::commit();
            return redirect()->back()->with([
                'type' => 'success',
                'message' => 'Friend request accepted successfully',
                'title' => 'Accepted'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with([
                'type' => 'error',
                'message' => 'Failed to accept friend request: ' . $e->getMessage(),
                'title' => 'Error'
            ]);
        }
    }

    /**
     * Reject a friend request
     */
    public function reject(string $id)
    {
        try {
            DB::beginTransaction();
            $friend = Friend::where('ids', $id)->firstOrFail();
            $friend->status = 'rejected';
            $friend->save();
            $this->user_notification($friend->user_id,'Friend Request Rejected',auth()->user()->name . ' has declined your friend request.');
            DB::commit();
            return redirect()->back()->with([
                'type' => 'success',
                'message' => 'Friend request rejected',
                'title' => 'Rejected'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with([
                'type' => 'error',
                'message' => 'Failed to reject friend request: ' . $e->getMessage(),
                'title' => 'Error'
            ]);
        }
    }
}

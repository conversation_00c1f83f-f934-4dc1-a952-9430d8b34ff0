@forelse ($bookings as $booking)
    <tr>
        <td data-label="Customer Name">{{ $booking->customer->name ?? '-' }}</td>
        <td data-label="Service Type">{{ $booking->service->category->name ?? '-' }}</td>
        <td data-label="Client Name">{{ $booking->service->name ?? '-' }}</td>
        <td data-label="Amount">${{ $booking->price ?? $booking->total_amount ?? 0 }}</td>
        @if ($booking->status == 0)
            @if($booking->hasTimePassed())
                <td data-label="Status" class="status ongoing-status">Ongoing</td>
            @else
                <td data-label="Status" class="status upcoming-status">Upcoming</td>
            @endif
        @elseif($booking->status == 1)
            <td data-label="Status" class="status paid-status">Completed</td>
        @elseif($booking->status == 2)
            <td data-label="Status" class="status unpaid-status">Cancelled</td>
        @else
            <td data-label="Status" class="status">-</td>
        @endif
        <td data-label="Date">
            {{ Carbon\Carbon::parse($booking->booking_date)->format('M d, Y') }}
        </td>
        <td data-label="Action">
            <div class="dropdown">
                <button class="drop-btn" type="button" id="dropdownMenuButton"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <li>
                        <a class="dropdown-item view fs-14 regular"
                           href="{{ route('booking.detail', ["booking_number" => $booking->booking_number, "ids" => $booking->ids]) }}">
                            <i class="bi bi-eye view-icon"></i>
                            View Details
                        </a>
                    </li>
                    @if($booking->status == 0)
                        @if($booking->hasTimePassed())
                            <li>
                                <button class="dropdown-item complete fs-14 regular booking-action" 
                                        type="button" 
                                        data-booking-id="{{ $booking->id }}" 
                                        data-action="complete">
                                    <i class="bi bi-check-circle complete-icon"></i>
                                    Mark as Complete
                                </button>
                            </li>
                        @endif
                        <li>
                            <button class="dropdown-item cancel fs-14 regular booking-action" 
                                    type="button"
                                    data-booking-id="{{ $booking->id }}" 
                                    data-action="cancel">
                                <i class="fa-solid fa-xmark cancel-icon"></i>
                                Cancel
                            </button>
                        </li>
                    @endif
                </ul>
            </div>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="7" class="text-center">No bookings found</td>
    </tr>
@endforelse
